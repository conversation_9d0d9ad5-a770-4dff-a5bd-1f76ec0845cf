const express = require('express');
const { body, param, query } = require('express-validator');
const chatController = require('../controllers/chatController');
const { authenticate, trackActivity, checkSubscription } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Validation rules
const sendMessageValidation = [
  body('message')
    .trim()
    .isLength({ min: 1, max: 2000 })
    .withMessage('Message must be between 1 and 2000 characters'),
  body('excelData')
    .optional()
    .isObject()
    .withMessage('Excel data must be an object'),
  body('threadId')
    .optional()
    .isString()
    .withMessage('Thread ID must be a string')
];

const historyValidation = [
  param('id')
    .notEmpty()
    .withMessage('Conversation ID is required'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('skip')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Skip must be a non-negative integer')
];

const clearConversationValidation = [
  param('id')
    .notEmpty()
    .withMessage('Conversation ID is required')
];

const feedbackValidation = [
  body('conversationId')
    .notEmpty()
    .withMessage('Conversation ID is required'),
  body('messageId')
    .optional()
    .isString()
    .withMessage('Message ID must be a string'),
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('feedback')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Feedback cannot exceed 1000 characters'),
  body('type')
    .optional()
    .isIn(['helpful', 'not_helpful', 'incorrect', 'inappropriate', 'other'])
    .withMessage('Invalid feedback type')
];

const conversationsValidation = [
  query('status')
    .optional()
    .isIn(['active', 'archived', 'deleted'])
    .withMessage('Invalid status'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  query('skip')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Skip must be a non-negative integer'),
  query('sortBy')
    .optional()
    .isIn(['updatedAt', 'createdAt', 'title'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters')
];

// Routes

/**
 * @route   POST /api/chat/message
 * @desc    Send a chat message
 * @access  Private
 */
router.post('/message', 
  authenticate, 
  trackActivity, 
  checkSubscription('free'),
  sendMessageValidation, 
  validateRequest, 
  chatController.sendMessage
);

/**
 * @route   GET /api/chat/history/:id
 * @desc    Get conversation history
 * @access  Private
 */
router.get('/history/:id', 
  authenticate, 
  trackActivity,
  historyValidation, 
  validateRequest, 
  chatController.getHistory
);

/**
 * @route   DELETE /api/chat/clear/:id
 * @desc    Clear conversation
 * @access  Private
 */
router.delete('/clear/:id', 
  authenticate, 
  trackActivity,
  clearConversationValidation, 
  validateRequest, 
  chatController.clearConversation
);

/**
 * @route   POST /api/chat/feedback
 * @desc    Submit feedback
 * @access  Private
 */
router.post('/feedback', 
  authenticate, 
  trackActivity,
  feedbackValidation, 
  validateRequest, 
  chatController.submitFeedback
);

/**
 * @route   GET /api/chat/conversations
 * @desc    Get user conversations list
 * @access  Private
 */
router.get('/conversations', 
  authenticate, 
  trackActivity,
  conversationsValidation, 
  validateRequest, 
  chatController.getConversations
);

/**
 * @route   GET /api/chat/conversations/:id
 * @desc    Get specific conversation details
 * @access  Private
 */
router.get('/conversations/:id', 
  authenticate, 
  trackActivity,
  param('id').notEmpty().withMessage('Conversation ID is required'),
  validateRequest, 
  chatController.getHistory
);

/**
 * @route   PUT /api/chat/conversations/:id/star
 * @desc    Star/unstar a conversation
 * @access  Private
 */
router.put('/conversations/:id/star', 
  authenticate, 
  trackActivity,
  param('id').notEmpty().withMessage('Conversation ID is required'),
  body('isStarred').isBoolean().withMessage('isStarred must be a boolean'),
  validateRequest,
  async (req, res) => {
    try {
      const { id } = req.params;
      const { isStarred } = req.body;
      const userId = req.user.userId;

      const Conversation = require('../models/Conversation');
      const conversation = await Conversation.findOne({
        $or: [
          { _id: id, userId },
          { threadId: id, userId }
        ]
      });

      if (!conversation) {
        return res.status(404).json({
          success: false,
          message: 'Conversation not found'
        });
      }

      conversation.isStarred = isStarred;
      await conversation.save();

      res.status(200).json({
        success: true,
        message: `Conversation ${isStarred ? 'starred' : 'unstarred'} successfully`,
        data: {
          conversationId: conversation._id,
          isStarred: conversation.isStarred
        }
      });
    } catch (error) {
      console.error('❌ Star conversation error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to update conversation',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }
);

/**
 * @route   PUT /api/chat/conversations/:id/archive
 * @desc    Archive/unarchive a conversation
 * @access  Private
 */
router.put('/conversations/:id/archive', 
  authenticate, 
  trackActivity,
  param('id').notEmpty().withMessage('Conversation ID is required'),
  body('isArchived').isBoolean().withMessage('isArchived must be a boolean'),
  validateRequest,
  async (req, res) => {
    try {
      const { id } = req.params;
      const { isArchived } = req.body;
      const userId = req.user.userId;

      const Conversation = require('../models/Conversation');
      const conversation = await Conversation.findOne({
        $or: [
          { _id: id, userId },
          { threadId: id, userId }
        ]
      });

      if (!conversation) {
        return res.status(404).json({
          success: false,
          message: 'Conversation not found'
        });
      }

      conversation.status = isArchived ? 'archived' : 'active';
      await conversation.save();

      res.status(200).json({
        success: true,
        message: `Conversation ${isArchived ? 'archived' : 'unarchived'} successfully`,
        data: {
          conversationId: conversation._id,
          status: conversation.status
        }
      });
    } catch (error) {
      console.error('❌ Archive conversation error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to update conversation',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }
);

module.exports = router;
