{"version": 3, "file": "MongoBinaryDownload.js", "sourceRoot": "", "sources": ["../../src/util/MongoBinaryDownload.ts"], "names": [], "mappings": ";;;;AAAA,yDAAoB;AACpB,6BAA0B;AAC1B,6DAAwB;AACxB,2BAA4F;AAC5F,qEAA+B;AAC/B,uDAAyC;AACzC,+BAAmC;AACnC,yEAA6B;AAC7B,+DAA0B;AAC1B,mGAA8D;AAC9D,yDAAoD;AACpD,8EAAmF;AACnF,+DAA0B;AAC1B,mCAAuD;AACvD,qDAAkD;AAElD,uCAAqC;AACrC,qCAA+E;AAG/E,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,6BAA6B,CAAC,CAAC;AASjD;;GAEG;AACH,MAAa,mBAAmB;IAkD9B,uCAAuC;IAEvC,YAAY,IAAqB;;QAC/B,IAAA,iBAAS,EAAC,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;QAChG,MAAM,OAAO,GAAG,MAAA,IAAI,CAAC,OAAO,mCAAI,IAAA,uBAAa,EAAC,sCAAsB,CAAC,OAAO,CAAC,CAAC;QAC9E,IAAA,iBAAS,EACP,OAAO,OAAO,KAAK,QAAQ,EAC3B,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAC1D,CAAC;QAEF,wEAAwE;QACxE,IAAI,CAAC,UAAU,GAAG;YAChB,QAAQ,EAAE,MAAA,IAAI,CAAC,QAAQ,mCAAI,YAAE,CAAC,QAAQ,EAAE;YACxC,IAAI,EAAE,MAAA,IAAI,CAAC,IAAI,mCAAI,YAAE,CAAC,IAAI,EAAE;YAC5B,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,MAAA,IAAI,CAAC,QAAQ,mCAAI,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,SAAS,CAAC,CAAC;YACrF,YAAY,EAAE,MAAA,IAAI,CAAC,YAAY,mCAAI,EAAE;YACrC,EAAE,EAAE,MAAA,IAAI,CAAC,EAAE,mCAAI,EAAE,EAAE,EAAE,SAAS,EAAE;SACjC,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG;YAChB,OAAO,EAAE,CAAC;YACV,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,CAAC;YACV,aAAa,EAAE,CAAC;SACjB,CAAC;IACJ,CAAC;IAtED,wDAAwD;IACxD,wDAAwD;IAExD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED,IAAI,QAAQ,CAAC,GAAY;QACvB,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,GAAG,CAAC;IACjC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;IACrC,CAAC;IAED,IAAI,WAAW,CAAC,GAAW;QACzB,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC;IACpC,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED,IAAI,IAAI,CAAC,GAAW;QAClB,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,GAAG,CAAC;IAC7B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IACjC,CAAC;IAED,IAAI,OAAO,CAAC,GAAW;QACrB,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,CAAC;IAChC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED,IAAI,QAAQ,CAAC,GAAW;QACtB,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,GAAG,CAAC;IACjC,CAAC;IA+BD;;;OAGG;IACa,OAAO;;YACrB,MAAM,IAAI,GAAG,MAAM,+BAAc,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEnE,OAAO,+BAAc,CAAC,iBAAiB,CACrC,IAAI,CAAC,WAAW,EAChB,MAAM,+BAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CACzC,CAAC;QACJ,CAAC;KAAA;IAED;;;OAGG;IACG,aAAa;;YACjB,GAAG,CAAC,eAAe,CAAC,CAAC;YACrB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;YAExC,IAAI,MAAM,IAAA,kBAAU,EAAC,UAAU,CAAC,EAAE;gBAChC,GAAG,CAAC,+BAA+B,UAAU,8BAA8B,CAAC,CAAC;gBAE7E,OAAO,UAAU,CAAC;aACnB;YAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAClD,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACnC,MAAM,aAAU,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAExC,IAAI,MAAM,IAAA,kBAAU,EAAC,UAAU,CAAC,EAAE;gBAChC,OAAO,UAAU,CAAC;aACnB;YAED,MAAM,IAAI,KAAK,CAAC,iDAAiD,UAAU,GAAG,CAAC,CAAC;QAClF,CAAC;KAAA;IAED;;;OAGG;IACG,aAAa;;YACjB,GAAG,CAAC,eAAe,CAAC,CAAC;YACrB,MAAM,MAAM,GAAG,IAAI,gCAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE3D,MAAM,IAAA,aAAK,EAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE9B,IAAI;gBACF,MAAM,aAAU,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,cAAS,CAAC,IAAI,GAAG,cAAS,CAAC,IAAI,CAAC,CAAC,CAAC,sGAAsG;aACnL;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,CAAC,KAAK,CACX,0BAA0B,IAAI,CAAC,WAAW,qEAAqE;oBAC7G,6CAA6C,CAChD,CAAC;gBACF,MAAM,GAAG,CAAC;aACX;YAED,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,cAAc,EAAE,CAAC;YAElD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAExD,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,WAAW,MAAM,EAAE,cAAc,CAAC,CAAC;YAE9D,OAAO,cAAc,CAAC;QACxB,CAAC;KAAA;IAED;;;;;;;;OAQG;IACG,YAAY,CAChB,kBAA0B,EAC1B,cAAsB;;YAEtB,GAAG,CAAC,oDAAoD,CAAC,CAAC;YAE1D,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,GAAG,CAAC,oCAAoC,CAAC,CAAC;gBAE1C,OAAO,SAAS,CAAC;aAClB;YAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YAC/D,MAAM,gBAAgB,GAAG,CAAC,MAAM,aAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACvF,MAAM,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YAC/D,MAAM,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACvD,MAAM,WAAW,GAAG,kBAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjD,GAAG,CAAC,4BAA4B,WAAW,iBAAiB,YAAY,EAAE,CAAC,CAAC;YAE5E,IAAI,YAAY,KAAK,WAAW,EAAE;gBAChC,MAAM,IAAI,4BAAmB,CAAC,WAAW,EAAE,YAAY,IAAI,SAAS,CAAC,CAAC;aACvE;YAED,MAAM,aAAU,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAExC,OAAO,IAAI,CAAC;QACd,CAAC;KAAA;IAED;;;;OAIG;IACG,QAAQ,CAAC,WAAmB;;YAChC,GAAG,CAAC,UAAU,CAAC,CAAC;YAChB,MAAM,KAAK,GACT,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;gBAC/B,OAAO,CAAC,GAAG,CAAC,UAAU;gBACtB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,gBAAgB;gBAC5B,OAAO,CAAC,GAAG,CAAC,WAAW;gBACvB,OAAO,CAAC,GAAG,CAAC,UAAU;gBACtB,OAAO,CAAC,GAAG,CAAC,WAAW;gBACvB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;YAEzB,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,MAAM,CAAC;YAE/D,MAAM,SAAS,GAAG,IAAI,SAAG,CAAC,WAAW,CAAC,CAAC;YACvC,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,IAAI,KAAK,CAAC;YAEzC,MAAM,cAAc,GAAmB;gBACrC,MAAM,EAAE,KAAK;gBACb,kBAAkB,EAAE,SAAS;gBAC7B,QAAQ,EAAE,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ;gBACxF,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,mCAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;aACtD,CAAC;YAEF,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAErD,IAAI,CAAC,QAAQ,EAAE;gBACb,MAAM,IAAI,KAAK,CAAC,kDAAkD,WAAW,GAAG,CAAC,CAAC;aACnF;YAED,MAAM,gBAAgB,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAClE,MAAM,oBAAoB,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,QAAQ,cAAc,CAAC,CAAC;YACvF,GAAG,CAAC,wBAAwB,KAAK,CAAC,CAAC,CAAC,eAAe,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,WAAW,GAAG,CAAC,CAAC;YAEtF,IAAI,MAAM,IAAA,kBAAU,EAAC,gBAAgB,CAAC,EAAE;gBACtC,GAAG,CAAC,+DAA+D,CAAC,CAAC;gBAErE,OAAO,gBAAgB,CAAC;aACzB;YAED,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YAErC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAC5C,SAAS,EACT,cAAc,EACd,gBAAgB,EAChB,oBAAoB,CACrB,CAAC;YAEF,OAAO,cAAc,CAAC;QACxB,CAAC;KAAA;IAED;;;;OAIG;IACG,OAAO,CAAC,cAAsB;;;YAClC,GAAG,CAAC,SAAS,CAAC,CAAC;YACf,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;YAC7C,GAAG,CAAC,sBAAsB,cAAc,aAAa,eAAe,GAAG,CAAC,CAAC;YAEzE,MAAM,IAAA,aAAK,EAAC,cAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;YAE3C,MAAM,MAAM,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,iCAAiC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE9E,IAAI,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;gBAC1C,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;aAClE;iBAAM,IAAI,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;gBACvC,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;aAChE;iBAAM;gBACL,MAAM,IAAI,KAAK,CACb,6CAA6C,cAAc,uBACzD,MAAA,IAAI,CAAC,eAAe,mCAAI,SAC1B,2CAA2C,CAC5C,CAAC;aACH;YAED,IAAI,CAAC,CAAC,MAAM,IAAA,kBAAU,EAAC,eAAe,CAAC,CAAC,EAAE;gBACxC,MAAM,IAAI,KAAK,CACb,kDAAkD,cAAc,uBAC9D,MAAA,IAAI,CAAC,eAAe,mCAAI,SAC1B,2CAA2C,CAC5C,CAAC;aACH;YAED,OAAO,eAAe,CAAC;;KACxB;IAED;;;;;OAKG;IACG,YAAY,CAChB,cAAsB,EACtB,WAAmB,EACnB,MAAiC;;YAEjC,GAAG,CAAC,cAAc,CAAC,CAAC;YACpB,MAAM,OAAO,GAAG,oBAAG,CAAC,OAAO,EAAE,CAAC;YAC9B,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE;gBAC3C,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;oBACvB,MAAM,CAAC,IAAI,CACT,IAAA,sBAAiB,EAAC,WAAW,EAAE;wBAC7B,IAAI,EAAE,KAAK;qBACZ,CAAC,CACH,CAAC;iBACH;gBAED,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC/B,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,CAAC,CAAC,CAAC;YAEH,OAAO,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBAC9B,IAAA,qBAAgB,EAAC,cAAc,CAAC;qBAC7B,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;oBACnB,GAAG,CAAC,IAAI,wBAAe,CAAC,yBAAyB,GAAG,cAAc,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;gBACpF,CAAC,CAAC;qBACD,IAAI,CAAC,IAAA,kBAAW,GAAE,CAAC;qBACnB,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;oBACnB,GAAG,CAAC,IAAI,wBAAe,CAAC,yBAAyB,GAAG,cAAc,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;gBACpF,CAAC,CAAC;qBACD,IAAI,CAAC,OAAO,CAAC;qBACb,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;oBACnB,GAAG,CAAC,IAAI,wBAAe,CAAC,yBAAyB,GAAG,cAAc,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;gBACpF,CAAC,CAAC;qBACD,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;QACL,CAAC;KAAA;IAED;;;;;OAKG;IACG,UAAU,CACd,cAAsB,EACtB,WAAmB,EACnB,MAAiC;;YAEjC,GAAG,CAAC,YAAY,CAAC,CAAC;YAElB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,eAAK,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;oBACjE,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE;wBACnB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;qBACpB;oBAED,OAAO,CAAC,SAAS,EAAE,CAAC;oBAEpB,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;oBAEnC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;wBAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;4BAC3B,OAAO,OAAO,CAAC,SAAS,EAAE,CAAC;yBAC5B;wBAED,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;4BACxC,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE;gCACd,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;6BACrB;4BAED,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;4BACvC,CAAC,CAAC,IAAI,CACJ,IAAA,sBAAiB,EAAC,WAAW,EAAE;gCAC7B,IAAI,EAAE,KAAK;6BACZ,CAAC,CACH,CAAC;wBACJ,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;KAAA;IAED;;;;;OAKG;IACG,YAAY,CAChB,GAAQ,EACR,WAA2B,EAC3B,gBAAwB,EACxB,oBAA4B;;YAE5B,GAAG,CAAC,cAAc,CAAC,CAAC;YACpB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YAEnD,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;YACzF,MAAM,eAAe,mBACnB,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,IACxD,WAAW,CACf,CAAC;YAEF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,GAAG,CAAC,qCAAqC,WAAW,GAAG,CAAC,CAAC;gBACzD,wBAAK;qBACF,GAAG,CAAC,GAAG,EAAE,eAAe,EAAE,CAAC,QAAQ,EAAE,EAAE;oBACtC,IAAI,QAAQ,CAAC,UAAU,IAAI,GAAG,EAAE;wBAC9B,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;4BAC/B,MAAM,CACJ,IAAI,sBAAa,CACf,WAAW,EACX,sCAAsC;gCACpC,4EAA4E;gCAC5E,0FAA0F;gCAC1F,gDAAgD;gCAChD,oEAAoE,CACvE,CACF,CAAC;4BAEF,OAAO;yBACR;wBAED,MAAM,CACJ,IAAI,sBAAa,CAAC,WAAW,EAAE,gCAAgC,QAAQ,CAAC,UAAU,GAAG,CAAC,CACvF,CAAC;wBAEF,OAAO;qBACR;oBACD,IAAI,OAAO,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,QAAQ,EAAE;wBACzD,MAAM,CAAC,IAAI,sBAAa,CAAC,WAAW,EAAE,4CAA4C,CAAC,CAAC,CAAC;wBAErF,OAAO;qBACR;oBAED,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,CAAC,CAAC;oBAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC1E,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;oBAEnF,MAAM,UAAU,GAAG,IAAA,sBAAiB,EAAC,oBAAoB,CAAC,CAAC;oBAE3D,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAE1B,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAS,EAAE;;wBACjC,IACE,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM;4BAChD,CAAC,CAAA,MAAA,WAAW,CAAC,IAAI,0CAAE,QAAQ,CAAC,MAAM,CAAC,CAAA,EACnC;4BACA,MAAM,CACJ,IAAI,sBAAa,CACf,WAAW,EACX,cAAc,IAAI,CAAC,UAAU,CAAC,OAAO,mCAAmC,CACzE,CACF,CAAC;4BAEF,OAAO;yBACR;wBAED,IAAI,CAAC,qBAAqB,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;wBAEhD,UAAU,CAAC,KAAK,EAAE,CAAC;wBACnB,MAAM,aAAU,CAAC,MAAM,CAAC,oBAAoB,EAAE,gBAAgB,CAAC,CAAC;wBAChE,GAAG,CAAC,wBAAwB,oBAAoB,SAAS,gBAAgB,GAAG,CAAC,CAAC;wBAE9E,OAAO,CAAC,gBAAgB,CAAC,CAAC;oBAC5B,CAAC,CAAA,CAAC,CAAC;oBAEH,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAU,EAAE,EAAE;wBACjC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;oBACpC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC;qBACD,EAAE,CAAC,OAAO,EAAE,CAAC,GAAU,EAAE,EAAE;oBAC1B,sCAAsC;oBACtC,OAAO,CAAC,KAAK,CAAC,qBAAqB,WAAW,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;oBACjE,MAAM,CAAC,IAAI,sBAAa,CAAC,WAAW,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;gBACtD,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACL,CAAC;KAAA;IAED;;;OAGG;IACH,qBAAqB,CAAC,KAAyB,EAAE,aAAsB,KAAK;QAC1E,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC;QAExC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;YAC7D,OAAO;SACR;QAED,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,GAAG,CAAC;QAEpC,MAAM,eAAe,GACnB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;QACrF,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;QAE7E,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9D,MAAM,OAAO,GAAG,wBAAwB,IAAI,CAAC,OAAO,MAAM,eAAe,MAAM,UAAU,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,QAAQ,EAAE,CAAC;QAEzI,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;YACxB,sGAAsG;YACtG,IAAA,oBAAS,EAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,oEAAoE;YAClG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SAC/B;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SACtB;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,GAAQ;QAC3B,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC;QAEhC,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;CACF;AArfD,kDAqfC;AAED,kBAAe,mBAAmB,CAAC"}