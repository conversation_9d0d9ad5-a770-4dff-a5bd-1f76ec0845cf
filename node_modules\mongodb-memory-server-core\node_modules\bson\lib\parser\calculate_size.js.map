{"version": 3, "file": "calculate_size.js", "sourceRoot": "", "sources": ["../../src/parser/calculate_size.ts"], "names": [], "mappings": ";;;AAAA,iCAAgC;AAChC,oCAAmC;AAEnC,wCAA0C;AAC1C,iCAAuF;AAEvF,SAAgB,mBAAmB,CACjC,MAAgB,EAChB,kBAA4B,EAC5B,eAAyB;IAEzB,IAAI,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;IAExB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,WAAW,IAAI,gBAAgB,CAC7B,CAAC,CAAC,QAAQ,EAAE,EACZ,MAAM,CAAC,CAAC,CAAC,EACT,kBAAkB,EAClB,IAAI,EACJ,eAAe,CAChB,CAAC;SACH;KACF;SAAM;QACL,yDAAyD;QAEzD,IAAI,OAAO,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,CAAA,KAAK,UAAU,EAAE;YACxC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;SAC1B;QAED,iBAAiB;QACjB,KAAK,IAAM,GAAG,IAAI,MAAM,EAAE;YACxB,WAAW,IAAI,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;SAC/F;KACF;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AA/BD,kDA+BC;AAED,gBAAgB;AAChB,SAAS,gBAAgB,CACvB,IAAY;AACZ,8DAA8D;AAC9D,KAAU,EACV,kBAA0B,EAC1B,OAAe,EACf,eAAuB;IAFvB,mCAAA,EAAA,0BAA0B;IAC1B,wBAAA,EAAA,eAAe;IACf,gCAAA,EAAA,uBAAuB;IAEvB,yDAAyD;IACzD,IAAI,OAAO,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,CAAA,KAAK,UAAU,EAAE;QACvC,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;KACxB;IAED,QAAQ,OAAO,KAAK,EAAE;QACpB,KAAK,QAAQ;YACX,OAAO,CAAC,GAAG,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,eAAM,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QAC5F,KAAK,QAAQ;YACX,IACE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK;gBAC3B,KAAK,IAAI,SAAS,CAAC,UAAU;gBAC7B,KAAK,IAAI,SAAS,CAAC,UAAU,EAC7B;gBACA,IAAI,KAAK,IAAI,SAAS,CAAC,cAAc,IAAI,KAAK,IAAI,SAAS,CAAC,cAAc,EAAE;oBAC1E,SAAS;oBACT,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;iBAC3E;qBAAM;oBACL,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;iBAC3E;aACF;iBAAM;gBACL,SAAS;gBACT,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aAC3E;QACH,KAAK,WAAW;YACd,IAAI,OAAO,IAAI,CAAC,eAAe;gBAC7B,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACtE,OAAO,CAAC,CAAC;QACX,KAAK,SAAS;YACZ,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5E,KAAK,QAAQ;YACX,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBACvF,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACrE;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,UAAU,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,UAAU,EAAE;gBACjF,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;aAC5E;iBAAM,IAAI,KAAK,YAAY,IAAI,IAAI,IAAA,cAAM,EAAC,KAAK,CAAC,EAAE;gBACjD,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aAC3E;iBAAM,IACL,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;gBACzB,KAAK,YAAY,WAAW;gBAC5B,IAAA,wBAAgB,EAAC,KAAK,CAAC,EACvB;gBACA,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,UAAU,CAC1F,CAAC;aACH;iBAAM,IACL,KAAK,CAAC,WAAW,CAAC,KAAK,MAAM;gBAC7B,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ;gBAC/B,KAAK,CAAC,WAAW,CAAC,KAAK,WAAW,EAClC;gBACA,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aAC3E;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;gBAC9C,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;aAC5E;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,MAAM,EAAE;gBACxC,0DAA0D;gBAC1D,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC9D,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxD,CAAC;wBACD,CAAC;wBACD,CAAC;wBACD,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC;wBAChD,CAAC;wBACD,mBAAmB,CAAC,KAAK,CAAC,KAAK,EAAE,kBAAkB,EAAE,eAAe,CAAC,CACtE,CAAC;iBACH;qBAAM;oBACL,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxD,CAAC;wBACD,CAAC;wBACD,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC;wBAChD,CAAC,CACF,CAAC;iBACH;aACF;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,IAAM,MAAM,GAAW,KAAK,CAAC;gBAC7B,qCAAqC;gBACrC,IAAI,MAAM,CAAC,QAAQ,KAAK,eAAM,CAAC,kBAAkB,EAAE;oBACjD,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxD,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAClC,CAAC;iBACH;qBAAM;oBACL,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CACzF,CAAC;iBACH;aACF;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxD,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC;oBACtC,CAAC;oBACD,CAAC;oBACD,CAAC,CACF,CAAC;aACH;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE;gBACzC,0CAA0C;gBAC1C,IAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAClC;oBACE,IAAI,EAAE,KAAK,CAAC,UAAU;oBACtB,GAAG,EAAE,KAAK,CAAC,GAAG;iBACf,EACD,KAAK,CAAC,MAAM,CACb,CAAC;gBAEF,gCAAgC;gBAChC,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,EAAE;oBACpB,cAAc,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;iBAClC;gBAED,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxD,CAAC;oBACD,mBAAmB,CAAC,cAAc,EAAE,kBAAkB,EAAE,eAAe,CAAC,CACzE,CAAC;aACH;iBAAM,IAAI,KAAK,YAAY,MAAM,IAAI,IAAA,gBAAQ,EAAC,KAAK,CAAC,EAAE;gBACrD,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxD,CAAC;oBACD,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC;oBACvC,CAAC;oBACD,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1B,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzB,CAAC,CACF,CAAC;aACH;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;gBAC9C,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxD,CAAC;oBACD,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC;oBACxC,CAAC;oBACD,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC;oBACxC,CAAC,CACF,CAAC;aACH;iBAAM;gBACL,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxD,mBAAmB,CAAC,KAAK,EAAE,kBAAkB,EAAE,eAAe,CAAC;oBAC/D,CAAC,CACF,CAAC;aACH;QACH,KAAK,UAAU;YACb,yDAAyD;YACzD,IAAI,KAAK,YAAY,MAAM,IAAI,IAAA,gBAAQ,EAAC,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,iBAAiB,EAAE;gBAC1F,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxD,CAAC;oBACD,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC;oBACvC,CAAC;oBACD,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1B,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzB,CAAC,CACF,CAAC;aACH;iBAAM;gBACL,IAAI,kBAAkB,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;oBACpF,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxD,CAAC;wBACD,CAAC;wBACD,CAAC;wBACD,eAAM,CAAC,UAAU,CAAC,IAAA,gCAAwB,EAAC,KAAK,CAAC,EAAE,MAAM,CAAC;wBAC1D,CAAC;wBACD,mBAAmB,CAAC,KAAK,CAAC,KAAK,EAAE,kBAAkB,EAAE,eAAe,CAAC,CACtE,CAAC;iBACH;qBAAM,IAAI,kBAAkB,EAAE;oBAC7B,OAAO,CACL,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxD,CAAC;wBACD,CAAC;wBACD,eAAM,CAAC,UAAU,CAAC,IAAA,gCAAwB,EAAC,KAAK,CAAC,EAAE,MAAM,CAAC;wBAC1D,CAAC,CACF,CAAC;iBACH;aACF;KACJ;IAED,OAAO,CAAC,CAAC;AACX,CAAC"}