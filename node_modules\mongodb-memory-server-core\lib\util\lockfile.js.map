{"version": 3, "file": "lockfile.js", "sourceRoot": "", "sources": ["../../src/util/lockfile.ts"], "names": [], "mappings": ";;;;AAAA,mCAAsC;AACtC,4DAAiC;AACjC,+DAA0B;AAC1B,wDAA6B;AAC7B,2BAA4C;AAC5C,6CAAoC;AACpC,+BAAoC;AACpC,qCAAmF;AAEnF,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAEtC;;GAEG;AACH,MAAM,WAAY,SAAQ,KAAK;IAC7B,YAAmB,MAAe;QAChC,KAAK,EAAE,CAAC;QADS,WAAM,GAAN,MAAM,CAAS;IAElC,CAAC;CACF;AAED,IAAY,cAiBX;AAjBD,WAAY,cAAc;IACxB;;OAEG;IACH,6DAAS,CAAA;IACT;;OAEG;IACH,6EAAiB,CAAA;IACjB;;OAEG;IACH,+DAAU,CAAA;IACV;;OAEG;IACH,yEAAe,CAAA;AACjB,CAAC,EAjBW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAiBzB;AAED,IAAY,cAGX;AAHD,WAAY,cAAc;IACxB,+BAAa,CAAA;IACb,mCAAiB,CAAA;AACnB,CAAC,EAHW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAGzB;AASD,4BAA4B;AAC5B,MAAM,mBAAoB,SAAQ,qBAAY;CAAG;AAEjD,MAAa,QAAQ;IA0LnB,YAAY,IAAY,EAAE,IAAY;QACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IArLD;;;OAGG;IACH,MAAM,CAAO,IAAI,CAAC,IAAY;;YAC5B,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC;YAC1B,GAAG,CAAC,uBAAuB,IAAI,GAAG,CAAC,CAAC;YAEpC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAE1C,yDAAyD;YACzD,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC,CAAC;YAE7F,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAC7C,QAAQ,MAAM,EAAE;gBACd,KAAK,cAAc,CAAC,eAAe,CAAC;gBACpC,KAAK,cAAc,CAAC,UAAU;oBAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACnC,KAAK,cAAc,CAAC,SAAS;oBAC3B,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBAClC;oBACE,MAAM,IAAI,mCAA0B,CAAC,MAAM,CAAC,CAAC;aAChD;QACH,CAAC;KAAA;IAED;;;OAGG;IACO,MAAM,CAAO,SAAS,CAAC,IAAY,EAAE,IAAa;;YAC1D,GAAG,CAAC,wBAAwB,IAAI,iBAAiB,IAAI,GAAG,CAAC,CAAC;YAE1D,uDAAuD;YACvD,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE;gBACnC,OAAO,cAAc,CAAC,SAAS,CAAC;aACjC;YAED,IAAI;gBACF,MAAM,QAAQ,GAAG,CAAC,MAAM,aAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAChF,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEtC,IAAI,OAAO,KAAK,OAAO,CAAC,GAAG,EAAE;oBAC3B,GAAG,CACD,+EAA+E,QAAQ,CAAC,CAAC,CAAC,GAAG,CAC9F,CAAC;oBAEF,0EAA0E;oBAC1E,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBACzB,OAAO,cAAc,CAAC,SAAS,CAAC;qBACjC;oBAED,4FAA4F;oBAC5F,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;wBAClC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;4BACzB,CAAC,CAAC,cAAc,CAAC,iBAAiB;4BAClC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC;qBAC/B;oBAED,+BAA+B;oBAC/B,OAAO,cAAc,CAAC,UAAU,CAAC;iBAClC;gBAED,GAAG,CAAC,iEAAiE,OAAO,GAAG,CAAC,CAAC;gBAEjF,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC;aAC3F;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;oBACrD,GAAG,CAAC,4CAA4C,CAAC,CAAC;oBAElD,OAAO,cAAc,CAAC,SAAS,CAAC;iBACjC;gBAED,MAAM,GAAG,CAAC;aACX;QACH,CAAC;KAAA;IAED;;;OAGG;IACO,MAAM,CAAO,WAAW,CAAC,IAAY;;YAC7C,GAAG,CAAC,2CAA2C,IAAI,GAAG,CAAC,CAAC;YACxD,gDAAgD;YAChD,IAAI,QAAQ,GAA+B,SAAS,CAAC;YACrD,oHAAoH;YACpH,IAAI,OAAO,GAAoC,SAAS,CAAC;YACzD,MAAM,IAAI,OAAO,CAAO,CAAC,GAAG,EAAE,EAAE;gBAC9B,OAAO,GAAG,CAAC,YAAY,EAAE,EAAE;oBACzB,IAAI,YAAY,KAAK,IAAI,EAAE;wBACzB,GAAG,EAAE,CAAC;qBACP;gBACH,CAAC,CAAC;gBAEF,QAAQ,GAAG,WAAW,CAAC,GAAS,EAAE;oBAChC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAC9C,GAAG,CAAC,mCAAmC,IAAI,kBAAkB,UAAU,GAAG,CAAC,CAAC;oBAE5E,IAAI,UAAU,KAAK,cAAc,CAAC,SAAS,EAAE;wBAC3C,GAAG,EAAE,CAAC;qBACP;gBACH,CAAC,CAAA,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB;gBAEhC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YAEH,IAAI,QAAQ,EAAE;gBACZ,aAAa,CAAC,QAAQ,CAAC,CAAC;aACzB;YACD,IAAI,OAAO,EAAE;gBACX,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;aAC5D;YAED,GAAG,CAAC,uCAAuC,IAAI,GAAG,CAAC,CAAC;YAEpD,gDAAgD;YAChD,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,gDAAgD;YAC3E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC9C,GAAG,CAAC,wDAAwD,IAAI,MAAM,UAAU,EAAE,CAAC,CAAC;YAEpF,QAAQ,UAAU,EAAE;gBAClB,KAAK,cAAc,CAAC,eAAe,CAAC;gBACpC,KAAK,cAAc,CAAC,UAAU;oBAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAChC,KAAK,cAAc,CAAC,SAAS;oBAC3B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC/B;oBACE,MAAM,IAAI,mCAA0B,CAAC,UAAU,CAAC,CAAC;aACpD;QACH,CAAC;KAAA;IAED;;;OAGG;IACO,MAAM,CAAO,UAAU,CAAC,IAAY;;YAC5C,8EAA8E;YAC9E,GAAG,CAAC,iDAAiD,IAAI,GAAG,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,IAAA,SAAM,GAAE,CAAC;YAEtB,qGAAqG;YACrG,IAAI;gBACF,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAS,EAAE;oBACvC,qGAAqG;oBACrG,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBACxB,GAAG,CAAC,qCAAqC,IAAI,GAAG,CAAC,CAAC;wBAElD,MAAM,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;qBAC7B;oBAED,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;oBAEtC,MAAM,aAAU,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;oBAEtE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC9C,CAAC,CAAA,CAAC,CAAC;aACJ;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,GAAG,YAAY,WAAW,IAAI,GAAG,CAAC,MAAM,EAAE;oBAC5C,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBAC/B;aACF;YAED,GAAG,CAAC,2CAA2C,IAAI,GAAG,CAAC,CAAC;YAExD,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;KAAA;IAkBD;;OAEG;IACG,MAAM;;;YACV,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC;YAC1B,GAAG,CAAC,2BAA2B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YAE7C,IAAI,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA,MAAA,IAAI,CAAC,IAAI,0CAAE,MAAM,KAAI,CAAC,EAAE;gBAChE,GAAG,CAAC,iCAAiC,CAAC,CAAC;gBAEvC,OAAO;aACR;YAED,iFAAiF;YACjF,QAAQ,MAAM,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;gBACtD,KAAK,cAAc,CAAC,SAAS;oBAC3B,GAAG,CAAC,yDAAyD,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;oBAC3E,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;oBAEhC,OAAO;gBACT,KAAK,cAAc,CAAC,iBAAiB;oBACnC,GAAG,CAAC,yDAAyD,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;oBAC3E,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;oBAE/B,OAAO;gBACT,KAAK,cAAc,CAAC,UAAU;oBAC5B,MAAM,IAAI,oCAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzD;oBACE,MAAM,IAAI,oCAA2B,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;aAC3D;;KACF;IAED;;;OAGG;IACa,aAAa,CAAC,SAAkB,IAAI;;YAClD,OAAO,MAAM,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,GAAS,EAAE;gBAClD,GAAG,CAAC,4BAA4B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;gBAE9C,IAAI,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACtC,OAAO;iBACR;gBAED,IAAI,MAAM,EAAE;oBACV,MAAM,aAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;wBAClD,GAAG,CAAC,4CAA4C,MAAM,GAAG,CAAC,CAAC;oBAC7D,CAAC,CAAC,CAAC;iBACJ;gBAED,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEvD,0EAA0E;gBAC1E,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;gBACtB,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;YACxB,CAAC,CAAA,CAAC,CAAC;QACL,CAAC;KAAA;;AAxPH,4BAyPC;AAxPC,iDAAiD;AAC1C,cAAK,GAAgB,IAAI,GAAG,EAAE,CAAC;AACtC,0CAA0C;AACnC,eAAM,GAAwB,IAAI,mBAAmB,EAAE,CAAC;AAC/D,iDAAiD;AAC1C,cAAK,GAAU,IAAI,mBAAK,EAAE,CAAC"}