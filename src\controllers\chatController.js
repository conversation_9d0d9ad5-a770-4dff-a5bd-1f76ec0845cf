const Conversation = require('../models/Conversation');
const User = require('../models/User');
const openaiService = require('../services/openaiService');
const dataOptimizer = require('../services/dataOptimizer');

class ChatController {
  // Send a chat message
  async sendMessage(req, res) {
    try {
      const { message, excelData, threadId } = req.body;
      const userId = req.user.userId;

      console.log(`💬 Processing message from user ${userId}`);

      // Initialize OpenAI service if not already done
      if (!openaiService.client) {
        openaiService.initialize();
      }

      let conversation;
      let openaiThreadId = threadId;

      // Find or create conversation
      if (threadId) {
        conversation = await Conversation.findOne({ threadId, userId });
        if (!conversation) {
          return res.status(404).json({
            success: false,
            message: 'Conversation not found'
          });
        }
      } else {
        // Create new conversation
        const thread = await openaiService.createThread();
        openaiThreadId = thread.id;

        conversation = new Conversation({
          userId,
          threadId: openaiThreadId,
          title: this.generateConversationTitle(message),
          description: 'Excel Chat AI conversation'
        });

        await conversation.save();
      }

      // Optimize Excel data if provided
      let optimizedExcelData = null;
      if (excelData) {
        const optimization = await dataOptimizer.optimizeForAI(excelData, 2000);
        optimizedExcelData = optimization.optimizedData;
        
        // Update conversation Excel context
        await conversation.updateExcelContext({
          hasExcelData: true,
          fileName: excelData.fileName || 'Unknown',
          fileSize: excelData.fileSize || 0,
          sheetNames: excelData.sheets ? excelData.sheets.map(s => s.name) : [],
          dataStructure: optimization
        });
      }

      // Add user message to conversation
      const userMessage = {
        role: 'user',
        content: message,
        timestamp: new Date(),
        metadata: {
          excelData: optimizedExcelData ? true : false
        }
      };

      await conversation.addMessage(userMessage);

      // Send message to OpenAI
      const aiResponse = await openaiService.sendMessage(
        openaiThreadId,
        message,
        optimizedExcelData
      );

      // Add assistant message to conversation
      const assistantMessage = {
        role: 'assistant',
        content: aiResponse.content,
        timestamp: new Date(),
        metadata: {
          tokens: aiResponse.usage ? aiResponse.usage.total_tokens : 0,
          model: 'gpt-4-turbo-preview',
          processingTime: aiResponse.processingTime,
          functionCalls: aiResponse.functionCalls || []
        }
      };

      await conversation.addMessage(assistantMessage);

      // Update user usage statistics
      const user = await User.findById(userId);
      if (user) {
        await user.incrementUsage('message');
      }

      res.status(200).json({
        success: true,
        message: 'Message sent successfully',
        data: {
          conversationId: conversation._id,
          threadId: openaiThreadId,
          response: aiResponse.content,
          processingTime: aiResponse.processingTime,
          usage: aiResponse.usage,
          functionCalls: aiResponse.functionCalls
        }
      });
    } catch (error) {
      console.error('❌ Chat message error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to process message',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Get conversation history
  async getHistory(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.userId;
      const { limit = 50, skip = 0 } = req.query;

      const conversation = await Conversation.findOne({
        $or: [
          { _id: id, userId },
          { threadId: id, userId }
        ]
      });

      if (!conversation) {
        return res.status(404).json({
          success: false,
          message: 'Conversation not found'
        });
      }

      // Get messages with pagination
      const messages = conversation.messages
        .slice(-limit - skip, -skip || undefined)
        .reverse();

      res.status(200).json({
        success: true,
        message: 'Conversation history retrieved successfully',
        data: {
          conversation: {
            id: conversation._id,
            threadId: conversation.threadId,
            title: conversation.title,
            description: conversation.description,
            status: conversation.status,
            excelContext: conversation.excelContext,
            analytics: conversation.analytics,
            createdAt: conversation.createdAt,
            updatedAt: conversation.updatedAt
          },
          messages,
          pagination: {
            total: conversation.messages.length,
            limit: parseInt(limit),
            skip: parseInt(skip),
            hasMore: conversation.messages.length > parseInt(limit) + parseInt(skip)
          }
        }
      });
    } catch (error) {
      console.error('❌ Get history error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve conversation history',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Clear conversation
  async clearConversation(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.userId;

      const conversation = await Conversation.findOne({
        $or: [
          { _id: id, userId },
          { threadId: id, userId }
        ]
      });

      if (!conversation) {
        return res.status(404).json({
          success: false,
          message: 'Conversation not found'
        });
      }

      // Clear messages
      conversation.messages = [];
      conversation.analytics.totalMessages = 0;
      conversation.analytics.totalTokens = 0;
      conversation.analytics.functionCallsCount = 0;
      conversation.analytics.lastActivity = new Date();

      await conversation.save();

      // Optionally delete the OpenAI thread
      try {
        await openaiService.deleteThread(conversation.threadId);
      } catch (error) {
        console.warn('⚠️ Failed to delete OpenAI thread:', error.message);
      }

      res.status(200).json({
        success: true,
        message: 'Conversation cleared successfully',
        data: {
          conversationId: conversation._id,
          threadId: conversation.threadId
        }
      });
    } catch (error) {
      console.error('❌ Clear conversation error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to clear conversation',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Submit feedback
  async submitFeedback(req, res) {
    try {
      const { conversationId, messageId, rating, feedback, type } = req.body;
      const userId = req.user.userId;

      // For now, just log the feedback
      // In a production system, you'd store this in a feedback collection
      console.log('📝 Feedback received:', {
        userId,
        conversationId,
        messageId,
        rating,
        feedback,
        type,
        timestamp: new Date()
      });

      res.status(200).json({
        success: true,
        message: 'Feedback submitted successfully',
        data: {
          feedbackId: `feedback_${Date.now()}`,
          timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('❌ Submit feedback error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to submit feedback',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Get user conversations list
  async getConversations(req, res) {
    try {
      const userId = req.user.userId;
      const { 
        status = 'active', 
        limit = 20, 
        skip = 0, 
        sortBy = 'updatedAt', 
        sortOrder = 'desc',
        search 
      } = req.query;

      let conversations;

      if (search) {
        conversations = await Conversation.searchConversations(userId, search, {
          limit: parseInt(limit),
          skip: parseInt(skip)
        });
      } else {
        conversations = await Conversation.findUserConversations(userId, {
          status,
          limit: parseInt(limit),
          skip: parseInt(skip),
          sortBy,
          sortOrder: sortOrder === 'desc' ? -1 : 1
        });
      }

      res.status(200).json({
        success: true,
        message: 'Conversations retrieved successfully',
        data: {
          conversations: conversations.map(conv => ({
            id: conv._id,
            threadId: conv.threadId,
            title: conv.title,
            description: conv.description,
            status: conv.status,
            analytics: conv.analytics,
            excelContext: {
              hasExcelData: conv.excelContext.hasExcelData,
              fileName: conv.excelContext.fileName
            },
            isStarred: conv.isStarred,
            tags: conv.tags,
            createdAt: conv.createdAt,
            updatedAt: conv.updatedAt
          })),
          pagination: {
            total: conversations.length,
            limit: parseInt(limit),
            skip: parseInt(skip),
            hasMore: conversations.length === parseInt(limit)
          }
        }
      });
    } catch (error) {
      console.error('❌ Get conversations error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve conversations',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Helper method to generate conversation title
  generateConversationTitle(message) {
    const words = message.split(' ').slice(0, 6);
    let title = words.join(' ');
    
    if (title.length > 50) {
      title = title.substring(0, 47) + '...';
    }
    
    return title || 'New Conversation';
  }
}

module.exports = new ChatController();
