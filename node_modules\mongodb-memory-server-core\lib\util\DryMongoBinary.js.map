{"version": 3, "file": "DryMongoBinary.js", "sourceRoot": "", "sources": ["../../src/util/DryMongoBinary.ts"], "names": [], "mappings": ";;;;AAAA,+DAA0B;AAC1B,mDAAoG;AACpG,mCAA2F;AAC3F,wDAA6B;AAC7B,2BAA6C;AAC7C,iFAA0C;AAC1C,mCAA2D;AAC3D,qCAA+F;AAC/F,qEAAkE;AAElE,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,wBAAwB,CAAC,CAAC;AA+C5C;;GAEG;AACH,MAAa,cAAc;IAMzB;;;OAGG;IACH,MAAM,CAAO,YAAY,CAAC,IAA2B;;YACnD,GAAG,CAAC,sDAAsD,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;YAC3E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEjD,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE;gBAC1B,GAAG,CAAC,+DAA+D,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC;gBAE5F,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBAEpE,IAAI,IAAA,yBAAiB,EAAC,YAAY,CAAC,EAAE;oBACnC,MAAM,IAAI,iCAAwB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;iBAC1D;gBAED,OAAO,YAAY,CAAC;aACrB;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBACtC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAClD,GAAG,CAAC,qDAAqD,IAAI,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC;gBAEtF,OAAO,MAAM,CAAC;aACf;YAED,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAClD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE7D,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;gBACnB,GAAG,CAAC,gDAAgD,CAAC,CAAC;gBAEtD,OAAO,SAAS,CAAC;aAClB;YAED,GAAG,CAAC,kCAAkC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACzD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAEnD,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC;KAAA;IAED;;OAEG;IACH,MAAM,CAAO,eAAe,CAC1B,IAA4B;;;YAE5B,GAAG,CAAC,iBAAiB,CAAC,CAAC;YACvB,MAAM,cAAc,GAAG,MAAA,IAAA,6BAAa,EAAC,sCAAsB,CAAC,OAAO,CAAC,mCAAI,+BAAe,CAAC;YACxF,MAAM,WAAW,GAA0B,IAAA,yBAAiB,EAAC,IAAI,CAAC;gBAChE,CAAC,CAAC,EAAE,OAAO,EAAE,cAAc,EAAE;gBAC7B,CAAC,CAAC,IAAI,CAAC;YAET,MAAM,KAAK,GAAoC;gBAC7C,OAAO,EAAE,WAAW,CAAC,OAAO,IAAI,cAAc;gBAC9C,WAAW,EACT,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC,IAAI,WAAW,CAAC,WAAW,IAAI,EAAE;gBACrF,EAAE,EAAE,MAAA,WAAW,CAAC,EAAE,mCAAI,CAAC,MAAM,IAAA,aAAK,GAAE,CAAC;gBACrC,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI,IAAA,aAAQ,GAAE;gBAC5C,IAAI,EAAE,WAAW,CAAC,IAAI,IAAI,IAAA,SAAI,GAAE;gBAChC,YAAY,EACV,IAAA,6BAAa,EAAC,sCAAsB,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,YAAY,IAAI,EAAE;aACxF,CAAC;YAEF,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE9E,YAAY;YACZ,IACE,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC;gBAClD,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC,EAClD;gBACA,kDAAkD;gBAClD,4IAA4I;gBAC5I,MAAM,KAAK,GAAG,CAAC,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC;oBAC/D,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC,CAAW,CAAC;gBAEhE,GAAG,CACD,oGAAoG,KAAK,IAAI,CAC9G,CAAC;gBAEF,OAAO,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;aACjD;YAED,OAAO,KAAK,CAAC;;KACd;IAED;;;;;OAKG;IACH,MAAM,CAAC,qBAAqB,CAC1B,KAAa,EACb,IAAqC;QAErC,GAAG,CAAC,kCAAkC,KAAK,IAAI,CAAC,CAAC;QAEjD,MAAM,cAAc,GAClB,4IAA4I,CAAC,IAAI,CAC/I,KAAK,CACN,CAAC;QAEJ,IAAA,iBAAS,EAAC,CAAC,IAAA,yBAAiB,EAAC,cAAc,CAAC,EAAE,IAAI,0BAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;QAE9E,oHAAoH;QACpH,IAAA,iBAAS,EAAC,CAAC,IAAA,yBAAiB,EAAC,cAAc,CAAC,MAAM,CAAC,EAAE,IAAI,0BAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;QAE/F,MAAM,MAAM,GAAqC,cAAc,CAAC,MAAM,CAAC;QAEvE,IAAA,iBAAS,EACP,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAC/D,IAAI,+BAAsB,CAAC,SAAS,CAAC,CACtC,CAAC;QACF,2HAA2H;QAC3H,IAAA,iBAAS,EACP,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EACjE,IAAI,+BAAsB,CAAC,UAAU,CAAC,CACvC,CAAC;QACF,IAAA,iBAAS,EACP,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,EAC1D,IAAI,+BAAsB,CAAC,MAAM,CAAC,CACnC,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QAExB,IAAI,MAAM,CAAC,QAAQ,KAAK,OAAO,EAAE;YAC/B,MAAM,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAEhF,IAAI,CAAC,EAAE,GAAG;gBACR,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,OAAO,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAG,CAAC,CAAC,CAAA,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;gBACvE,wDAAwD;gBACxD,OAAO,EAAE,EAAE;aACZ,CAAC;SACH;aAAM;YACL,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAa,CAAC;SAC9C;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,MAAM,CAAO,aAAa,CAAC,IAA+B;;YACxD,GAAG,CAAC,eAAe,CAAC,CAAC;YAErB,IAAI,UAAkB,CAAC;YAEvB,IAAI,IAAA,yBAAS,EAAC,IAAA,6BAAa,EAAC,sCAAsB,CAAC,gCAAgC,CAAC,CAAC,EAAE;gBACrF,MAAM,WAAW,GAAG,MAAM,IAAI,+CAAsB,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;gBAC5E,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;aAC3C;iBAAM;gBACL,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvD,MAAM,IAAI,GAAG,IAAA,iBAAS,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;gBAE5D,UAAU,GAAG,UAAU,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC;aACrE;YAED,OAAO,UAAU,CAAC;QACpB,CAAC;KAAA;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,QAAgB,EAAE,UAAkB;QAC3D,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAEzB,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAO,aAAa,CAAC,YAAoB;;YAC7C,2DAA2D;YAC3D,GAAG,CAAC,eAAe,CAAC,CAAC;YACrB,IAAI;gBACF,MAAM,IAAA,8BAAsB,EAAC,YAAY,CAAC,CAAC;gBAE3C,GAAG,CAAC,+CAA+C,YAAY,GAAG,CAAC,CAAC;gBAEpE,OAAO,YAAY,CAAC,CAAC,oCAAoC;aAC1D;YAAC,OAAO,GAAG,EAAE;gBACZ,GAAG,CACD,+CAA+C,YAAY,OACzD,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GACvC,EAAE,CACH,CAAC;aACH;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;KAAA;IAED;;;;;OAKG;IACH,MAAM,CAAO,aAAa,CACxB,IAAuD;;YAEvD,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;YAC3B,MAAM,KAAK,GAAwB;gBACjC,eAAe,EAAE,EAAE;gBACnB,YAAY,EAAE,EAAE;gBAChB,QAAQ,EAAE,EAAE;gBACZ,aAAa,EAAE,EAAE;aAClB,CAAC;YACF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAClD,+CAA+C;YAE/C,gEAAgE;YAChE,mEAAmE;YACnE,IAAI,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;YAChE,4FAA4F;YAC5F,OAAO,gBAAgB,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,GAAG,uBAAuB,CAAC,EAAE;gBAChF,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;aAC/D;YAED,MAAM,eAAe,GAAG,IAAA,wBAAY,EAAC;gBACnC,IAAI,EAAE,uBAAuB;gBAC7B,GAAG,EAAE,gBAAgB;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,IAAA,yBAAiB,EAAC,eAAe,CAAC,EAAE;gBACvC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,UAAU,CAAC,CAAC;aACxF;YAED,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,yBAAyB,CAAC,CAAC;YAEhF,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;YAE5E,kFAAkF;YAClF,MAAM,kBAAkB,GACtB,IAAI,CAAC,WAAW,IAAI,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC,CAAC;YAEzE,IAAI,CAAC,IAAA,yBAAiB,EAAC,kBAAkB,CAAC,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3E,GAAG,CAAC,gDAAgD,CAAC,CAAC;gBACtD,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;aAC9E;YAED,qDAAqD;YACrD,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CACrC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,kBAAkB,CAAC,EAC/C,UAAU,CACX,CAAC;YAEF,OAAO,KAAK,CAAC;QACf,CAAC;KAAA;IAED;;;OAGG;IACH,MAAM,CAAO,oBAAoB,CAC/B,IAAuD;;YAEvD,MAAM,YAAY,GAAG,IAAA,yBAAS,EAAC,IAAA,6BAAa,EAAC,sCAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC;YACzF,GAAG,CAAC,kEAAkE,YAAY,GAAG,CAAC,CAAC;YACvF,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAE7C,GAAG,CAAC,8BAA8B,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAE9D,iEAAiE;YACjE,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,MAAM,IAAA,kBAAU,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE;gBAChE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAE5D,IAAI,CAAC,IAAA,yBAAiB,EAAC,OAAO,CAAC,EAAE;oBAC/B,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBACxB;aACF;YAED,wDAAwD;YACxD,IAAI,MAAM,IAAA,kBAAU,EAAC,KAAK,CAAC,aAAa,CAAC,EAAE;gBACzC,GAAG,CACD,wEAAwE,KAAK,CAAC,aAAa,GAAG,CAC/F,CAAC;gBAEF,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;aACpC;YACD,IAAI,MAAM,IAAA,kBAAU,EAAC,KAAK,CAAC,eAAe,CAAC,EAAE;gBAC3C,GAAG,CAAC,2DAA2D,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC;gBAEzF,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;aACtC;YACD,IAAI,MAAM,IAAA,kBAAU,EAAC,KAAK,CAAC,YAAY,CAAC,EAAE;gBACxC,GAAG,CAAC,wDAAwD,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;gBAEnF,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;aACnC;YACD,IAAI,MAAM,IAAA,kBAAU,EAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;gBACpC,GAAG,CAAC,oDAAoD,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;gBAE3E,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;aAC/B;YAED,oEAAoE;YACpE,GAAG,CAAC,yDAAyD,IAAI,CAAC,OAAO,aAAa,CAAC,CAAC;YAExF,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClC,GAAG,CAAC,6DAA6D,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC;gBAEzF,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;aACrC;YACD,IAAI,YAAY,IAAI,CAAC,CAAC,KAAK,CAAC,eAAe,EAAE;gBAC3C,GAAG,CAAC,sDAAsD,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC;gBAEpF,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;aACvC;YACD,4FAA4F;YAC5F,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjC,GAAG,CAAC,6CAA6C,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;gBAExE,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;aACpC;YAED,GAAG,CAAC,yCAAyC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;YAEhE,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;QACjC,CAAC;KAAA;IAED;;;OAGG;IACK,MAAM,CAAC,OAAO;QACpB,OAAO,IAAA,YAAO,GAAE,CAAC;IACnB,CAAC;;AArVH,wCAsVC;AArVC;;GAEG;AACI,0BAAW,GAAwB,IAAI,GAAG,EAAE,CAAC"}