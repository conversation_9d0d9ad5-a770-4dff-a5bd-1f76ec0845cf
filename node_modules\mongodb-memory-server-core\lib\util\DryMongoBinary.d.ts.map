{"version": 3, "file": "DryMongoBinary.d.ts", "sourceRoot": "", "sources": ["../../src/util/DryMongoBinary.ts"], "names": [], "mappings": "AAMA,OAAO,EAAS,KAAK,EAAsB,MAAM,SAAS,CAAC;AAM3D,2EAA2E;AAC3E,MAAM,WAAW,yBAAyB;IACxC,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,EAAE,CAAC,EAAE,KAAK,CAAC;IACX,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,mFAAmF;AACnF,MAAM,WAAW,qBAAsB,SAAQ,yBAAyB;IACtE,OAAO,EAAE,WAAW,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC,CAAC;CAC5D;AAED,qFAAqF;AACrF,MAAM,WAAW,yBAAyB;IACxC,OAAO,EAAE,WAAW,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC;IACvD,IAAI,EAAE,WAAW,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC;IACjD,QAAQ,EAAE,WAAW,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC;IACxD,EAAE,EAAE,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;CAC9C;AAED,yFAAyF;AACzF,MAAM,WAAW,mBAAmB;IAClC,6CAA6C;IAC7C,aAAa,EAAE,MAAM,CAAC;IACtB,wCAAwC;IACxC,eAAe,EAAE,MAAM,CAAC;IACxB,oEAAoE;IACpE,YAAY,EAAE,MAAM,CAAC;IACrB,0DAA0D;IAC1D,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,gCAAgC;IAC/C,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;GAEG;AACH,qBAAa,cAAc;IACzB;;OAEG;IACH,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAa;IAEpD;;;OAGG;WACU,YAAY,CAAC,IAAI,EAAE,qBAAqB,GAAG,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;IAsCnF;;OAEG;WACU,eAAe,CAC1B,IAAI,CAAC,EAAE,qBAAqB,GAC3B,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;IAwC3C;;;;;OAKG;IACH,MAAM,CAAC,qBAAqB,CAC1B,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,QAAQ,CAAC,qBAAqB,CAAC,GACpC,QAAQ,CAAC,qBAAqB,CAAC;IAgDlC;;;OAGG;WACU,aAAa,CAAC,IAAI,EAAE,yBAAyB,GAAG,OAAO,CAAC,MAAM,CAAC;IAkB5E;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,MAAM;IAMtE;;;;OAIG;WACU,aAAa,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;IAoB7E;;;;;OAKG;WACU,aAAa,CACxB,IAAI,EAAE,qBAAqB,GAAG,yBAAyB,GACtD,OAAO,CAAC,mBAAmB,CAAC;IAkD/B;;;OAGG;WACU,oBAAoB,CAC/B,IAAI,EAAE,qBAAqB,GAAG,yBAAyB,GACtD,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAiE7B;;;OAGG;IACH,OAAO,CAAC,MAAM,CAAC,OAAO;CAGvB"}