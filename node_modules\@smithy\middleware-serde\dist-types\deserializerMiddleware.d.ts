import { DeserializeMiddleware, ResponseDeserializer, SerdeContext, SerdeFun<PERSON> } from "@smithy/types";
/**
 * @internal
 * @deprecated will be replaced by schemaSerdePlugin from core/schema.
 */
export declare const deserializerMiddleware: <Input extends object = any, Output extends object = any, CommandSerdeContext extends SerdeContext = any>(options: SerdeFunctions, deserializer: ResponseDeserializer<any, any, CommandSerdeContext>) => DeserializeMiddleware<Input, Output>;
