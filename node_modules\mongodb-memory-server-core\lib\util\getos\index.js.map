{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/util/getos/index.ts"], "names": [], "mappings": ";;;;AAAA,2BAA8B;AAC9B,+DAA0B;AAC1B,oCAA6D;AAE7D,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,eAAe,CAAC,CAAC;AAEnC,yDAAyD;AACzD,MAAM,QAAQ,GAAG;IACf,sFAAsF;IACtF,IAAI,EAAE,4CAA4C;IAClD,QAAQ,EAAE,4CAA4C;IACtD,OAAO,EAAE,0CAA0C;CACpD,CAAC;AAEF,0DAA0D;AAC1D,MAAM,OAAO,GAAG;IACd,IAAI,EAAE,wBAAwB;IAC9B,QAAQ,EAAE,iCAAiC;IAC3C,OAAO,EAAE,0CAA0C;IACnD,OAAO,EAAE,iCAAiC;CAC3C,CAAC;AAEF,+DAA+D;AAClD,QAAA,OAAO,GAAG,SAAS,CAAC;AAgBjC;;;GAGG;AACH,SAAgB,SAAS,CAAC,EAAS;IACjC,OAAO,EAAE,CAAC,EAAE,KAAK,OAAO,CAAC;AAC3B,CAAC;AAFD,8BAEC;AAED;;GAEG;AACH,IAAI,QAA2B,CAAC;AAEhC,uBAAuB;AACvB,SAAsB,KAAK;;QACzB,IAAI,CAAC,QAAQ,EAAE;YACb,qDAAqD;YACrD,MAAM,MAAM,GAAG,IAAA,aAAQ,GAAE,CAAC;YAE1B,2BAA2B;YAC3B,IAAI,MAAM,KAAK,OAAO,EAAE;gBACtB,QAAQ,GAAG,MAAM,mBAAmB,EAAE,CAAC;aACxC;iBAAM;gBACL,QAAQ,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC;aAC3B;SACF;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CAAA;AAdD,sBAcC;AAED,sDAAsD;AACtD,SAAe,mBAAmB;;QAChC,8BAA8B;QAC9B,uCAAuC;QACvC,sDAAsD;QACtD,sDAAsD;QACtD,uCAAuC;QAEvC,MAAM,WAAW,GAAG,MAAM,IAAA,sBAAc,EAAC,mCAAmC,EAAE,QAAQ,CAAC,CAAC;QAExF,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE;YAC1B,GAAG,CAAC,wCAAwC,CAAC,CAAC;YAE9C,OAAO,WAAW,CAAC;SACpB;QAED,MAAM,YAAY,GAAG,MAAM,IAAA,sBAAc,EAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;QAEtE,IAAI,SAAS,CAAC,YAAY,CAAC,EAAE;YAC3B,GAAG,CAAC,yCAAyC,CAAC,CAAC;YAE/C,OAAO,YAAY,CAAC;SACrB;QAED,MAAM,YAAY,GAAG,MAAM,IAAA,sBAAc,EAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;QAE1E,IAAI,SAAS,CAAC,YAAY,CAAC,EAAE;YAC3B,GAAG,CAAC,yCAAyC,CAAC,CAAC;YAE/C,OAAO,YAAY,CAAC;SACrB;QAED,MAAM,aAAa,GAAG,MAAM,IAAA,sBAAc,EAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QAEzE,IAAI,SAAS,CAAC,aAAa,CAAC,EAAE;YAC5B,GAAG,CAAC,0CAA0C,CAAC,CAAC;YAEhD,OAAO,aAAa,CAAC;SACtB;QAED,OAAO,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;QAElF,qCAAqC;QACrC,OAAO;YACL,EAAE,EAAE,OAAO;YACX,IAAI,EAAE,eAAO;YACb,OAAO,EAAE,EAAE;SACZ,CAAC;IACJ,CAAC;CAAA;AAED;;;;GAIG;AACH,SAAgB,SAAS,CAAC,EAAuB;IAC/C,uBAAuB;IACvB,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,KAAK,eAAO,EAAE;QAC7B,GAAG,CAAC,+CAA+C,EAAE,EAAE,CAAC,CAAC;KAC1D;IAED,OAAO,CAAC,IAAA,yBAAiB,EAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,eAAO,CAAC;AACvD,CAAC;AAPD,8BAOC;AAED;;GAEG;AACH,SAAgB,QAAQ,CAAC,KAAa;;IACpC,OAAO;QACL,EAAE,EAAE,OAAO;QACX,IAAI,EAAE,MAAA,MAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,0CAAG,CAAC,EAAE,iBAAiB,EAAE,mCAAI,eAAO;QACpE,QAAQ,EAAE,MAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,0CAAG,CAAC,EAAE,iBAAiB,EAAE;QACjE,OAAO,EAAE,MAAA,MAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,0CAAG,CAAC,EAAE,iBAAiB,EAAE,mCAAI,EAAE;KACtE,CAAC;AACJ,CAAC;AAPD,4BAOC;AAED;;GAEG;AACH,SAAgB,OAAO,CAAC,KAAa;;IACnC,OAAO;QACL,EAAE,EAAE,OAAO;QACX,IAAI,EAAE,MAAA,MAAA,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,0CAAG,CAAC,EAAE,iBAAiB,EAAE,mCAAI,eAAO;QACnE,QAAQ,EAAE,MAAA,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,0CAAG,CAAC,EAAE,iBAAiB,EAAE;QAChE,OAAO,EAAE,MAAA,MAAA,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,0CAAG,CAAC,EAAE,iBAAiB,EAAE,mCAAI,EAAE;QACpE,OAAO,EAAE,MAAA,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,0CAAG,CAAC,EAAE,iBAAiB,GAAG,KAAK,CAAC,GAAG,CAAC;KAC1E,CAAC;AACJ,CAAC;AARD,0BAQC"}