// Test conversation fixtures
const conversations = {
  // Basic conversation
  basicConversation: {
    threadId: 'thread_basic_123',
    title: 'Basic Excel Help',
    description: 'Getting help with Excel formulas',
    status: 'active',
    messages: [
      {
        role: 'user',
        content: 'How do I create a SUM formula?',
        timestamp: new Date(),
        metadata: {
          tokens: 10,
          excelData: false
        }
      },
      {
        role: 'assistant',
        content: 'To create a SUM formula, use =SUM(range). For example, =SUM(A1:A10) will add all values from A1 to A10.',
        timestamp: new Date(),
        metadata: {
          tokens: 25,
          model: 'gpt-4-turbo-preview',
          processingTime: 1500,
          functionCalls: []
        }
      }
    ],
    excelContext: {
      hasExcelData: false,
      fileName: null,
      fileSize: 0,
      sheetNames: [],
      dataStructure: null,
      lastUpdated: null
    },
    analytics: {
      totalMessages: 2,
      totalTokens: 35,
      averageResponseTime: 1500,
      functionCallsCount: 0,
      lastActivity: new Date()
    },
    tags: ['formulas', 'sum'],
    isStarred: false
  },

  // Conversation with Excel data
  excelConversation: {
    threadId: 'thread_excel_456',
    title: 'Sales Data Analysis',
    description: 'Analyzing quarterly sales data',
    status: 'active',
    messages: [
      {
        role: 'user',
        content: 'Please analyze this sales data and suggest improvements',
        timestamp: new Date(),
        metadata: {
          tokens: 15,
          excelData: true
        }
      },
      {
        role: 'assistant',
        content: 'Based on your sales data, I can see trends in Q1-Q3. Here are my recommendations...',
        timestamp: new Date(),
        metadata: {
          tokens: 45,
          model: 'gpt-4-turbo-preview',
          processingTime: 2500,
          functionCalls: [
            {
              name: 'analyze_excel_data',
              parameters: { data: {}, analysis_type: 'statistics' },
              result: { insights: 'Sales trending upward' },
              timestamp: new Date()
            }
          ]
        }
      }
    ],
    excelContext: {
      hasExcelData: true,
      fileName: 'sales_q1_q3.xlsx',
      fileSize: 15360,
      sheetNames: ['Q1 Sales', 'Q2 Sales', 'Q3 Sales'],
      dataStructure: {
        sheetCount: 3,
        totalRows: 150,
        totalColumns: 8
      },
      lastUpdated: new Date()
    },
    analytics: {
      totalMessages: 2,
      totalTokens: 60,
      averageResponseTime: 2500,
      functionCallsCount: 1,
      lastActivity: new Date()
    },
    tags: ['analysis', 'sales', 'data'],
    isStarred: true
  },

  // Long conversation
  longConversation: {
    threadId: 'thread_long_789',
    title: 'Complex Financial Model',
    description: 'Building a comprehensive financial forecasting model',
    status: 'active',
    messages: [
      {
        role: 'user',
        content: 'I need help building a financial forecasting model',
        timestamp: new Date(Date.now() - 3600000), // 1 hour ago
        metadata: { tokens: 12, excelData: false }
      },
      {
        role: 'assistant',
        content: 'I\'d be happy to help you build a financial forecasting model. Let\'s start with the basic structure...',
        timestamp: new Date(Date.now() - 3590000),
        metadata: { tokens: 30, model: 'gpt-4-turbo-preview', processingTime: 2000, functionCalls: [] }
      },
      {
        role: 'user',
        content: 'How do I incorporate seasonality into the model?',
        timestamp: new Date(Date.now() - 3000000),
        metadata: { tokens: 15, excelData: false }
      },
      {
        role: 'assistant',
        content: 'For seasonality, you can use seasonal indices or moving averages. Here\'s how to implement them...',
        timestamp: new Date(Date.now() - 2990000),
        metadata: { tokens: 40, model: 'gpt-4-turbo-preview', processingTime: 1800, functionCalls: [] }
      },
      {
        role: 'user',
        content: 'Can you help me create formulas for revenue projections?',
        timestamp: new Date(Date.now() - 1800000),
        metadata: { tokens: 18, excelData: true }
      },
      {
        role: 'assistant',
        content: 'Certainly! For revenue projections, you\'ll want to use a combination of historical data and growth assumptions...',
        timestamp: new Date(Date.now() - 1790000),
        metadata: { 
          tokens: 55, 
          model: 'gpt-4-turbo-preview', 
          processingTime: 3000,
          functionCalls: [
            {
              name: 'generate_excel_formula',
              parameters: { requirement: 'revenue projection formula', data_context: {} },
              result: { formula: '=B2*(1+$C$1)^A2', explanation: 'Compound growth formula' },
              timestamp: new Date(Date.now() - 1790000)
            }
          ]
        }
      }
    ],
    excelContext: {
      hasExcelData: true,
      fileName: 'financial_model_v2.xlsx',
      fileSize: 45120,
      sheetNames: ['Assumptions', 'Revenue', 'Expenses', 'Cash Flow', 'Summary'],
      dataStructure: {
        sheetCount: 5,
        totalRows: 500,
        totalColumns: 15
      },
      lastUpdated: new Date(Date.now() - 1800000)
    },
    analytics: {
      totalMessages: 6,
      totalTokens: 170,
      averageResponseTime: 2266,
      functionCallsCount: 1,
      lastActivity: new Date(Date.now() - 1790000)
    },
    tags: ['financial', 'modeling', 'forecasting', 'revenue'],
    isStarred: true
  },

  // Archived conversation
  archivedConversation: {
    threadId: 'thread_archived_101',
    title: 'Old Project Discussion',
    description: 'Discussion about a completed project',
    status: 'archived',
    messages: [
      {
        role: 'user',
        content: 'Help me with this old spreadsheet',
        timestamp: new Date(Date.now() - 86400000 * 30), // 30 days ago
        metadata: { tokens: 8, excelData: false }
      },
      {
        role: 'assistant',
        content: 'I can help you with that spreadsheet. What specific issues are you facing?',
        timestamp: new Date(Date.now() - 86400000 * 30 + 60000),
        metadata: { tokens: 20, model: 'gpt-4-turbo-preview', processingTime: 1200, functionCalls: [] }
      }
    ],
    excelContext: {
      hasExcelData: false,
      fileName: null,
      fileSize: 0,
      sheetNames: [],
      dataStructure: null,
      lastUpdated: null
    },
    analytics: {
      totalMessages: 2,
      totalTokens: 28,
      averageResponseTime: 1200,
      functionCallsCount: 0,
      lastActivity: new Date(Date.now() - 86400000 * 30 + 60000)
    },
    tags: ['archived'],
    isStarred: false
  },

  // Shared conversation
  sharedConversation: {
    threadId: 'thread_shared_202',
    title: 'Team Budget Planning',
    description: 'Collaborative budget planning for the team',
    status: 'active',
    messages: [
      {
        role: 'user',
        content: 'Let\'s work on the team budget together',
        timestamp: new Date(),
        metadata: { tokens: 10, excelData: true }
      },
      {
        role: 'assistant',
        content: 'Great! I can help you create a comprehensive team budget. Let\'s start by categorizing expenses...',
        timestamp: new Date(),
        metadata: { tokens: 25, model: 'gpt-4-turbo-preview', processingTime: 1800, functionCalls: [] }
      }
    ],
    excelContext: {
      hasExcelData: true,
      fileName: 'team_budget_2024.xlsx',
      fileSize: 8192,
      sheetNames: ['Personnel', 'Equipment', 'Travel', 'Summary'],
      dataStructure: {
        sheetCount: 4,
        totalRows: 100,
        totalColumns: 6
      },
      lastUpdated: new Date()
    },
    analytics: {
      totalMessages: 2,
      totalTokens: 35,
      averageResponseTime: 1800,
      functionCallsCount: 0,
      lastActivity: new Date()
    },
    tags: ['budget', 'team', 'planning'],
    isStarred: false,
    sharedWith: [
      {
        permission: 'write',
        sharedAt: new Date()
      }
    ]
  }
};

// Helper function to create conversations for a user
const createConversations = async (userId, conversationKeys = []) => {
  const Conversation = require('../../src/models/Conversation');
  const createdConversations = {};

  for (const key of conversationKeys) {
    if (conversations[key]) {
      const conversationData = {
        ...conversations[key],
        userId
      };
      
      const conversation = new Conversation(conversationData);
      createdConversations[key] = await conversation.save();
    }
  }

  return createdConversations;
};

// Helper function to create a conversation with custom data
const createCustomConversation = async (userId, customData = {}) => {
  const Conversation = require('../../src/models/Conversation');
  const conversationData = {
    ...conversations.basicConversation,
    userId,
    ...customData
  };
  
  const conversation = new Conversation(conversationData);
  return await conversation.save();
};

// Helper function to get conversation data without creating
const getConversationData = (conversationKey) => {
  return conversations[conversationKey] ? { ...conversations[conversationKey] } : null;
};

// Helper function to get all conversation keys
const getConversationKeys = () => {
  return Object.keys(conversations);
};

// Helper function to create conversation with messages
const createConversationWithMessages = async (userId, messageCount = 5) => {
  const Conversation = require('../../src/models/Conversation');
  
  const messages = [];
  for (let i = 0; i < messageCount; i++) {
    const isUser = i % 2 === 0;
    messages.push({
      role: isUser ? 'user' : 'assistant',
      content: isUser ? `User message ${i + 1}` : `Assistant response ${i + 1}`,
      timestamp: new Date(Date.now() - (messageCount - i) * 60000), // 1 minute apart
      metadata: {
        tokens: isUser ? 10 : 25,
        model: isUser ? undefined : 'gpt-4-turbo-preview',
        processingTime: isUser ? undefined : 1500,
        functionCalls: []
      }
    });
  }

  const conversationData = {
    userId,
    threadId: `thread_test_${Date.now()}`,
    title: `Test Conversation with ${messageCount} messages`,
    description: 'Auto-generated test conversation',
    status: 'active',
    messages,
    analytics: {
      totalMessages: messageCount,
      totalTokens: messageCount * 17.5, // Average of user and assistant tokens
      averageResponseTime: 1500,
      functionCallsCount: 0,
      lastActivity: new Date()
    }
  };

  const conversation = new Conversation(conversationData);
  return await conversation.save();
};

module.exports = {
  conversations,
  createConversations,
  createCustomConversation,
  createConversationWithMessages,
  getConversationData,
  getConversationKeys
};
