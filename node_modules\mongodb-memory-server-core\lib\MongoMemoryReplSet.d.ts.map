{"version": 3, "file": "MongoMemoryReplSet.d.ts", "sourceRoot": "", "sources": ["../src/MongoMemoryReplSet.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,iBAAiB,EAAE,aAAa,EAAyB,MAAM,qBAAqB,CAAC;AAC9F,OAAO,EAGL,OAAO,EAMP,eAAe,EAIhB,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAGrD,OAAO,EAEL,uBAAuB,EACvB,2BAA2B,EAC3B,aAAa,EACd,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AAgB7C;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B;;;OAGG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,aAAa,CAAC;IAC/B;;;OAGG;IACH,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;IAChB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;OAGG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB;;;OAGG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ;;;OAGG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;;OAGG;IACH,KAAK,CAAC,EAAE,YAAY,CAAC;IACrB;;;OAGG;IACH,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B;;;OAGG;IACH,cAAc,CAAC,EAAE,gCAAgC,CAAC;CACnD;AAED;;GAEG;AACH,MAAM,WAAW,gCAAgC;IAC/C,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B,uBAAuB,CAAC,EAAE,MAAM,CAAC;IACjC,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,oBAAoB,CAAC,EAAE,MAAM,CAAC;CAC/B;AAED;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,YAAY,EAAE,2BAA2B,EAAE,CAAC;IAC5C;;OAEG;IACH,MAAM,EAAE,eAAe,CAAC;IACxB;;;OAGG;IACH,OAAO,EAAE,WAAW,CAAC;CACtB;AAED;;GAEG;AACH,oBAAY,wBAAwB;IAClC,IAAI,SAAS;IACb,OAAO,YAAY;IACnB,OAAO,YAAY;CACpB;AAED;;GAEG;AACH,oBAAY,wBAAwB;IAClC,WAAW,gBAAgB;CAC5B;AAED,MAAM,WAAW,kBAAmB,SAAQ,YAAY;IAEtD,IAAI,CAAC,KAAK,EAAE,wBAAwB,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;IAC/D,EAAE,CAAC,KAAK,EAAE,wBAAwB,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;IAC9E,IAAI,CAAC,KAAK,EAAE,wBAAwB,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;CACjF;AAED;;GAEG;AACH,qBAAa,kBAAmB,SAAQ,YAAa,YAAW,eAAe;IAC7E;;OAEG;IACH,OAAO,EAAE,iBAAiB,EAAE,CAAM;IAGlC,uCAAuC;IACvC,SAAS,CAAC,aAAa,EAAG,2BAA2B,EAAE,CAAC;IACxD,kDAAkD;IAClD,SAAS,CAAC,WAAW,EAAG,eAAe,CAAC;IACxC,gEAAgE;IAChE,SAAS,CAAC,YAAY,EAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;IAC/C,gDAAgD;IAChD,SAAS,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC;IAE/B,SAAS,CAAC,MAAM,EAAE,wBAAwB,CAAoC;IAC9E,SAAS,CAAC,cAAc,EAAE,OAAO,CAAS;gBAE9B,IAAI,GAAE,OAAO,CAAC,sBAAsB,CAAM;IAQtD;;;OAGG;IACH,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,wBAAwB,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAK/E;;;OAGG;WACU,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,sBAAsB,CAAC,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAQxF;;OAEG;IACH,IAAI,KAAK,IAAI,wBAAwB,CAEpC;IAED;;;OAGG;IACH,IAAI,YAAY,IAAI,2BAA2B,EAAE,CAEhD;IAED,IAAI,YAAY,CAAC,GAAG,EAAE,2BAA2B,EAAE,EAGlD;IAED;;;OAGG;IACH,IAAI,UAAU,IAAI,eAAe,CAEhC;IAED,IAAI,UAAU,CAAC,GAAG,EAAE,eAAe,EAGlC;IAED;;;;OAIG;IACH,IAAI,WAAW,IAAI,WAAW,CAE7B;IAED,IAAI,WAAW,CAAC,GAAG,EAAE,WAAW,EA0B/B;IAED;;;;OAIG;IACH,SAAS,CAAC,UAAU,IAAI,OAAO;IAY/B;;;;OAIG;IACH,SAAS,CAAC,eAAe,CACvB,QAAQ,GAAE,2BAAgC,EAC1C,eAAe,CAAC,EAAE,MAAM,GACvB,uBAAuB;IAwC1B;;;;;;;OAOG;IACH,MAAM,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM;IA6BlD;;;OAGG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAgC5B;;OAEG;cACa,cAAc,IAAI,OAAO,CAAC,IAAI,CAAC;IAoE/C;;;OAGG;cACa,aAAa,IAAI,OAAO,CAAC,MAAM,CAAC;IAyBhD;;;;;OAKG;IACG,IAAI,CAAC,UAAU,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IACjD;;;OAGG;IACG,IAAI,CAAC,cAAc,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAiDtD;;;;;;;;OAQG;IACG,OAAO,CAAC,KAAK,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAC5C;;;;;;OAMG;IACG,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IA0C/C;;;OAGG;IACG,gBAAgB,IAAI,OAAO,CAAC,IAAI,CAAC;IAgCvC;;;;;;OAMG;cACa,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC;IAoF7C;;;OAGG;IACH,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,uBAAuB,GAAG,iBAAiB;IAY/E;;;;;OAKG;cACa,eAAe,CAAC,OAAO,GAAE,MAAkB,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;CAsC5F;AAED,eAAe,kBAAkB,CAAC"}