const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const promClient = require('prom-client');

// Import configurations
const config = require('./config/environment');
const databaseConnection = require('./config/database');
const openaiConfig = require('./config/openai');

// Import utilities
const { logger, requestLogger, healthLogger } = require('./utils/logger');
const {
  errorHandler,
  notFoundHandler,
  handleUnhandledRejection,
  handleUncaughtException,
  handleGracefulShutdown,
  createSuccessResponse
} = require('./utils/errorHandler');

// Import middleware
const { generalLimiter, burstLimiter } = require('./middleware/rateLimiter');
const { sanitizeInput } = require('./middleware/validation');

// Import routes
const authRoutes = require('./routes/auth');
const chatRoutes = require('./routes/chat');
const excelRoutes = require('./routes/excel');

// Initialize Express app
const app = express();

// Set up error handlers for unhandled promises and exceptions
handleUnhandledRejection();
handleUncaughtException();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Compression middleware
app.use(compression());

// CORS configuration
const corsOptions = {
  origin: config.security.corsOrigins,
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};
app.use(cors(corsOptions));

// Rate limiting
app.use(burstLimiter);
app.use(generalLimiter);

// Body parsing middleware
app.use(express.json({
  limit: '10mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Input sanitization
app.use(sanitizeInput);

// Request logging
app.use(requestLogger);

// Prometheus metrics
const collectDefaultMetrics = promClient.collectDefaultMetrics;
collectDefaultMetrics({ timeout: 5000 });

// Custom metrics
const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
});

const openaiApiCalls = new promClient.Counter({
  name: 'openai_api_calls_total',
  help: 'Total number of OpenAI API calls',
  labelNames: ['model', 'status']
});

// Metrics middleware
app.use((req, res, next) => {
  const start = Date.now();

  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    httpRequestDuration
      .labels(req.method, req.route?.path || req.path, res.statusCode)
      .observe(duration);
  });

  next();
});

// Health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    const healthChecks = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.server.nodeEnv,
      version: '1.0.0',
      services: {}
    };

    // Database health check
    const dbHealth = await databaseConnection.healthCheck();
    healthChecks.services.database = dbHealth;

    // OpenAI health check
    const openaiHealth = await openaiConfig.healthCheck();
    healthChecks.services.openai = openaiHealth;

    // Memory usage
    const memUsage = process.memoryUsage();
    healthChecks.memory = {
      rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(memUsage.external / 1024 / 1024)}MB`
    };

    // Determine overall status
    const allServicesHealthy = Object.values(healthChecks.services)
      .every(service => service.status === 'healthy');

    if (!allServicesHealthy) {
      healthChecks.status = 'degraded';
    }

    const statusCode = healthChecks.status === 'healthy' ? 200 : 503;

    healthLogger.check('application', healthChecks.status, healthChecks);

    res.status(statusCode).json(createSuccessResponse(
      'Health check completed',
      healthChecks
    ));
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(503).json({
      success: false,
      message: 'Health check failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Metrics endpoint
app.get('/api/metrics', async (req, res) => {
  try {
    res.set('Content-Type', promClient.register.contentType);
    res.end(await promClient.register.metrics());
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve metrics',
      error: error.message
    });
  }
});

// API version endpoint
app.get('/api/version', (req, res) => {
  res.json(createSuccessResponse('API version information', {
    version: '1.0.0',
    name: 'Excel Chat AI Backend',
    description: 'Node.js/Express.js API service for Excel AI assistance',
    environment: config.server.nodeEnv,
    nodeVersion: process.version,
    uptime: process.uptime()
  }));
});

// Root endpoint
app.get('/', (req, res) => {
  res.json(createSuccessResponse('Excel Chat AI Backend API', {
    message: 'Welcome to Excel Chat AI Backend',
    version: '1.0.0',
    documentation: '/api/docs',
    health: '/api/health',
    metrics: '/api/metrics'
  }));
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/excel', excelRoutes);

// 404 handler for undefined routes
app.use('*', notFoundHandler);

// Global error handling middleware
app.use(errorHandler);

// Initialize database connection
const initializeDatabase = async () => {
  try {
    await databaseConnection.connect();
    logger.info('✅ Database connection established');
  } catch (error) {
    logger.error('❌ Database connection failed:', error);
    process.exit(1);
  }
};

// Initialize OpenAI service
const initializeOpenAI = async () => {
  try {
    openaiConfig.initialize();
    logger.info('✅ OpenAI service initialized');
  } catch (error) {
    logger.error('❌ OpenAI service initialization failed:', error);
    // Don't exit process, as the service might still work without OpenAI initially
  }
};

// Start server
const startServer = async () => {
  try {
    // Initialize services
    await initializeDatabase();
    await initializeOpenAI();

    // Start HTTP server
    const server = app.listen(config.server.port, config.server.host, () => {
      logger.info(`🚀 Excel Chat AI Backend running on ${config.server.host}:${config.server.port}`);
      logger.info(`📊 Health check: http://${config.server.host}:${config.server.port}/api/health`);
      logger.info(`📈 Metrics: http://${config.server.host}:${config.server.port}/api/metrics`);
      logger.info(`🌍 Environment: ${config.server.nodeEnv}`);
      logger.info(`📝 Log level: ${config.logging.level}`);
    });

    // Set up graceful shutdown
    handleGracefulShutdown(server);

    return server;
  } catch (error) {
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start the application
if (require.main === module) {
  startServer();
}

module.exports = { app, startServer, httpRequestDuration, openaiApiCalls };
