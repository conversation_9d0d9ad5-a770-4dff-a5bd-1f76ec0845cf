#!/usr/bin/env node

require('dotenv').config();
const mongoose = require('mongoose');
const config = require('../src/config/environment');
const { logger } = require('../src/utils/logger');

// Import models to ensure they're registered
const User = require('../src/models/User');
const Conversation = require('../src/models/Conversation');
const ExcelSession = require('../src/models/ExcelSession');

class DatabaseMigration {
  constructor() {
    this.migrations = [
      {
        version: '1.0.0',
        name: 'Initial Setup',
        description: 'Create initial database structure and indexes',
        up: this.migration_1_0_0_up.bind(this),
        down: this.migration_1_0_0_down.bind(this)
      }
    ];
  }

  async connect() {
    try {
      await mongoose.connect(config.database.uri, config.database.options);
      console.log('✅ Connected to MongoDB');
      logger.info('Database migration: Connected to MongoDB');
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error.message);
      logger.error('Database migration: Connection failed', error);
      throw error;
    }
  }

  async disconnect() {
    try {
      await mongoose.connection.close();
      console.log('🔌 Disconnected from MongoDB');
      logger.info('Database migration: Disconnected from MongoDB');
    } catch (error) {
      console.error('❌ MongoDB disconnection failed:', error.message);
      logger.error('Database migration: Disconnection failed', error);
    }
  }

  async createMigrationCollection() {
    try {
      const collections = await mongoose.connection.db.listCollections().toArray();
      const migrationExists = collections.some(col => col.name === 'migrations');
      
      if (!migrationExists) {
        await mongoose.connection.db.createCollection('migrations');
        console.log('✅ Created migrations collection');
      }
    } catch (error) {
      console.error('❌ Failed to create migrations collection:', error.message);
      throw error;
    }
  }

  async getAppliedMigrations() {
    try {
      const migrations = await mongoose.connection.db
        .collection('migrations')
        .find({})
        .sort({ appliedAt: 1 })
        .toArray();
      
      return migrations.map(m => m.version);
    } catch (error) {
      console.error('❌ Failed to get applied migrations:', error.message);
      return [];
    }
  }

  async recordMigration(version, name) {
    try {
      await mongoose.connection.db.collection('migrations').insertOne({
        version,
        name,
        appliedAt: new Date()
      });
      console.log(`✅ Recorded migration: ${version} - ${name}`);
    } catch (error) {
      console.error(`❌ Failed to record migration ${version}:`, error.message);
      throw error;
    }
  }

  async removeMigrationRecord(version) {
    try {
      await mongoose.connection.db.collection('migrations').deleteOne({ version });
      console.log(`✅ Removed migration record: ${version}`);
    } catch (error) {
      console.error(`❌ Failed to remove migration record ${version}:`, error.message);
      throw error;
    }
  }

  // Migration 1.0.0: Initial Setup
  async migration_1_0_0_up() {
    console.log('🔄 Running migration 1.0.0: Initial Setup');

    try {
      // Create indexes for User model
      await User.createIndexes();
      console.log('✅ Created User indexes');

      // Create indexes for Conversation model
      await Conversation.createIndexes();
      console.log('✅ Created Conversation indexes');

      // Create indexes for ExcelSession model
      await ExcelSession.createIndexes();
      console.log('✅ Created ExcelSession indexes');

      // Create admin user if it doesn't exist
      const adminExists = await User.findOne({ email: '<EMAIL>' });
      if (!adminExists) {
        const adminUser = new User({
          email: '<EMAIL>',
          password: 'AdminPassword123!',
          firstName: 'Admin',
          lastName: 'User',
          role: 'admin',
          isEmailVerified: true,
          subscription: {
            plan: 'enterprise',
            isActive: true,
            startDate: new Date(),
            endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
          }
        });
        
        await adminUser.save();
        console.log('✅ Created admin user (<EMAIL> / AdminPassword123!)');
        console.log('⚠️  Please change the admin password after first login!');
      } else {
        console.log('ℹ️  Admin user already exists');
      }

      // Set up database constraints and validations
      await this.setupDatabaseConstraints();

      console.log('✅ Migration 1.0.0 completed successfully');
    } catch (error) {
      console.error('❌ Migration 1.0.0 failed:', error.message);
      throw error;
    }
  }

  async migration_1_0_0_down() {
    console.log('🔄 Rolling back migration 1.0.0: Initial Setup');

    try {
      // Drop indexes
      await mongoose.connection.db.collection('users').dropIndexes();
      await mongoose.connection.db.collection('conversations').dropIndexes();
      await mongoose.connection.db.collection('excelsessions').dropIndexes();
      
      console.log('✅ Dropped all indexes');

      // Remove admin user
      await User.deleteOne({ email: '<EMAIL>' });
      console.log('✅ Removed admin user');

      console.log('✅ Migration 1.0.0 rollback completed');
    } catch (error) {
      console.error('❌ Migration 1.0.0 rollback failed:', error.message);
      throw error;
    }
  }

  async setupDatabaseConstraints() {
    try {
      // Add any additional database constraints here
      console.log('✅ Database constraints set up');
    } catch (error) {
      console.error('❌ Failed to set up database constraints:', error.message);
      throw error;
    }
  }

  async runMigrations() {
    try {
      await this.connect();
      await this.createMigrationCollection();

      const appliedMigrations = await this.getAppliedMigrations();
      console.log(`📊 Applied migrations: ${appliedMigrations.length}`);

      for (const migration of this.migrations) {
        if (!appliedMigrations.includes(migration.version)) {
          console.log(`\n🚀 Running migration ${migration.version}: ${migration.name}`);
          console.log(`📝 ${migration.description}`);
          
          await migration.up();
          await this.recordMigration(migration.version, migration.name);
          
          console.log(`✅ Migration ${migration.version} completed`);
        } else {
          console.log(`⏭️  Migration ${migration.version} already applied`);
        }
      }

      console.log('\n🎉 All migrations completed successfully!');
    } catch (error) {
      console.error('\n❌ Migration failed:', error.message);
      logger.error('Database migration failed', error);
      throw error;
    } finally {
      await this.disconnect();
    }
  }

  async rollbackMigration(version) {
    try {
      await this.connect();

      const migration = this.migrations.find(m => m.version === version);
      if (!migration) {
        throw new Error(`Migration ${version} not found`);
      }

      const appliedMigrations = await this.getAppliedMigrations();
      if (!appliedMigrations.includes(version)) {
        console.log(`⚠️  Migration ${version} is not applied`);
        return;
      }

      console.log(`\n🔄 Rolling back migration ${version}: ${migration.name}`);
      
      await migration.down();
      await this.removeMigrationRecord(version);
      
      console.log(`✅ Migration ${version} rolled back successfully`);
    } catch (error) {
      console.error('\n❌ Rollback failed:', error.message);
      logger.error('Database migration rollback failed', error);
      throw error;
    } finally {
      await this.disconnect();
    }
  }

  async status() {
    try {
      await this.connect();
      
      const appliedMigrations = await this.getAppliedMigrations();
      
      console.log('\n📊 Migration Status');
      console.log('==================');
      
      for (const migration of this.migrations) {
        const isApplied = appliedMigrations.includes(migration.version);
        const status = isApplied ? '✅ Applied' : '⏳ Pending';
        console.log(`${status} - ${migration.version}: ${migration.name}`);
      }
      
      console.log(`\nTotal migrations: ${this.migrations.length}`);
      console.log(`Applied: ${appliedMigrations.length}`);
      console.log(`Pending: ${this.migrations.length - appliedMigrations.length}`);
    } catch (error) {
      console.error('\n❌ Failed to get migration status:', error.message);
      throw error;
    } finally {
      await this.disconnect();
    }
  }
}

// CLI interface
async function main() {
  const migration = new DatabaseMigration();
  const command = process.argv[2];

  try {
    switch (command) {
      case 'up':
      case 'migrate':
        await migration.runMigrations();
        break;
      
      case 'down':
      case 'rollback':
        const version = process.argv[3];
        if (!version) {
          console.error('❌ Please specify a migration version to rollback');
          process.exit(1);
        }
        await migration.rollbackMigration(version);
        break;
      
      case 'status':
        await migration.status();
        break;
      
      default:
        console.log('📖 Usage:');
        console.log('  npm run db:migrate        - Run all pending migrations');
        console.log('  npm run db:migrate up     - Run all pending migrations');
        console.log('  npm run db:migrate down <version> - Rollback specific migration');
        console.log('  npm run db:migrate status - Show migration status');
        break;
    }
  } catch (error) {
    console.error('❌ Migration command failed:', error.message);
    process.exit(1);
  }
}

// Run CLI if called directly
if (require.main === module) {
  main();
}

module.exports = { DatabaseMigration };
