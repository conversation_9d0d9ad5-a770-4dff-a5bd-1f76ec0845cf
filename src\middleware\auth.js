const jwt = require('jsonwebtoken');
const User = require('../models/User');
const config = require('../config/environment');

// Authentication middleware
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.'
      });
    }

    // Extract token from "Bearer <token>" format
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader;

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Invalid token format.'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, config.auth.jwtSecret);
    
    // Find user
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. User not found.'
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Account is deactivated.'
      });
    }

    // Add user info to request
    req.user = {
      userId: user._id,
      email: user.email,
      role: user.role,
      fullName: user.fullName
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Invalid token.'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Token expired.'
      });
    }

    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      message: 'Authentication failed',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Authorization middleware for specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Authentication required.'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Insufficient permissions.'
      });
    }

    next();
  };
};

// Optional authentication middleware (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader) {
      return next();
    }

    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader;

    if (!token) {
      return next();
    }

    const decoded = jwt.verify(token, config.auth.jwtSecret);
    const user = await User.findById(decoded.userId);
    
    if (user && user.isActive) {
      req.user = {
        userId: user._id,
        email: user.email,
        role: user.role,
        fullName: user.fullName
      };
    }

    next();
  } catch (error) {
    // Silently continue without authentication
    next();
  }
};

// Middleware to check subscription status
const checkSubscription = (requiredPlan = 'free') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      const user = await User.findById(req.user.userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      const planHierarchy = {
        'free': 0,
        'basic': 1,
        'premium': 2,
        'enterprise': 3
      };

      const userPlanLevel = planHierarchy[user.subscription.plan] || 0;
      const requiredPlanLevel = planHierarchy[requiredPlan] || 0;

      if (userPlanLevel < requiredPlanLevel) {
        return res.status(403).json({
          success: false,
          message: `This feature requires a ${requiredPlan} subscription or higher`,
          currentPlan: user.subscription.plan,
          requiredPlan
        });
      }

      // Check if subscription is active (for paid plans)
      if (requiredPlan !== 'free' && !user.subscription.isActive) {
        return res.status(403).json({
          success: false,
          message: 'Subscription is not active',
          currentPlan: user.subscription.plan
        });
      }

      next();
    } catch (error) {
      console.error('Subscription check error:', error);
      res.status(500).json({
        success: false,
        message: 'Subscription verification failed'
      });
    }
  };
};

// Middleware to track user activity
const trackActivity = async (req, res, next) => {
  try {
    if (req.user && req.user.userId) {
      // Update user's last activity asynchronously
      User.findByIdAndUpdate(
        req.user.userId,
        { 'usage.lastActivity': new Date() },
        { new: false }
      ).catch(error => {
        console.error('Activity tracking error:', error);
      });
    }
    next();
  } catch (error) {
    // Don't fail the request if activity tracking fails
    console.error('Activity tracking middleware error:', error);
    next();
  }
};

module.exports = {
  authenticate,
  authorize,
  optionalAuth,
  checkSubscription,
  trackActivity
};
