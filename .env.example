# Server Configuration
NODE_ENV=development
PORT=3001
HOST=localhost

# Database
MONGODB_URI=mongodb+srv://vutov23:<EMAIL>/excel-chat-ai
MONGODB_TEST_URI=mongodb+srv://vutov23:<EMAIL>/excel-chat-ai-test

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ASSISTANT_ID=asst_your_assistant_id_here
OPENAI_MODEL=gpt-4-turbo-preview

# Authentication
JWT_SECRET=your_jwt_secret_here_make_it_long_and_secure
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Security
CORS_ORIGINS=https://localhost:3000,https://your-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads
ALLOWED_FILE_TYPES=.xlsx,.xls,.csv

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
