#!/bin/bash

# Excel Chat AI Backend Deployment Script
# Usage: ./scripts/deploy.sh [environment] [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="production"
SKIP_TESTS=false
SKIP_BUILD=false
FORCE_DEPLOY=false
BACKUP_DB=true

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [environment] [options]"
    echo ""
    echo "Environments:"
    echo "  production    Deploy to production (default)"
    echo "  staging       Deploy to staging"
    echo "  development   Deploy to development"
    echo ""
    echo "Options:"
    echo "  --skip-tests     Skip running tests"
    echo "  --skip-build     Skip building Docker images"
    echo "  --force          Force deployment without confirmation"
    echo "  --no-backup      Skip database backup"
    echo "  --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 production"
    echo "  $0 staging --skip-tests"
    echo "  $0 development --force --no-backup"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        production|staging|development)
            ENVIRONMENT="$1"
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --force)
            FORCE_DEPLOY=true
            shift
            ;;
        --no-backup)
            BACKUP_DB=false
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Set environment-specific variables
case $ENVIRONMENT in
    production)
        COMPOSE_FILE="docker-compose.yml"
        ENV_FILE=".env.production"
        ;;
    staging)
        COMPOSE_FILE="docker-compose.staging.yml"
        ENV_FILE=".env.staging"
        ;;
    development)
        COMPOSE_FILE="docker-compose.dev.yml"
        ENV_FILE=".env.development"
        ;;
esac

print_status "Starting deployment to $ENVIRONMENT environment"

# Check if required files exist
if [[ ! -f "$COMPOSE_FILE" ]]; then
    print_error "Docker Compose file not found: $COMPOSE_FILE"
    exit 1
fi

if [[ ! -f "$ENV_FILE" ]] && [[ "$ENVIRONMENT" != "development" ]]; then
    print_warning "Environment file not found: $ENV_FILE"
    print_warning "Using default .env file"
    ENV_FILE=".env"
fi

# Load environment variables
if [[ -f "$ENV_FILE" ]]; then
    print_status "Loading environment variables from $ENV_FILE"
    export $(grep -v '^#' "$ENV_FILE" | xargs)
fi

# Pre-deployment checks
print_status "Running pre-deployment checks..."

# Check Docker
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed or not in PATH"
    exit 1
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

# Run tests
if [[ "$SKIP_TESTS" == false ]]; then
    print_status "Running tests..."
    if npm test; then
        print_success "All tests passed"
    else
        print_error "Tests failed"
        exit 1
    fi
else
    print_warning "Skipping tests"
fi

# Build Docker images
if [[ "$SKIP_BUILD" == false ]]; then
    print_status "Building Docker images..."
    if docker-compose -f "$COMPOSE_FILE" build; then
        print_success "Docker images built successfully"
    else
        print_error "Failed to build Docker images"
        exit 1
    fi
else
    print_warning "Skipping Docker build"
fi

# Database backup
if [[ "$BACKUP_DB" == true ]] && [[ "$ENVIRONMENT" == "production" ]]; then
    print_status "Creating database backup..."
    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).gz"
    
    if docker-compose -f "$COMPOSE_FILE" exec -T mongo mongodump --archive --gzip > "backups/$BACKUP_FILE"; then
        print_success "Database backup created: backups/$BACKUP_FILE"
    else
        print_warning "Failed to create database backup"
    fi
fi

# Deployment confirmation
if [[ "$FORCE_DEPLOY" == false ]]; then
    echo ""
    print_warning "About to deploy to $ENVIRONMENT environment"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled"
        exit 0
    fi
fi

# Stop existing services
print_status "Stopping existing services..."
docker-compose -f "$COMPOSE_FILE" down

# Start services
print_status "Starting services..."
if docker-compose -f "$COMPOSE_FILE" up -d; then
    print_success "Services started successfully"
else
    print_error "Failed to start services"
    exit 1
fi

# Wait for services to be healthy
print_status "Waiting for services to be healthy..."
sleep 30

# Health check
print_status "Performing health check..."
if curl -f http://localhost:${PORT:-3001}/api/health > /dev/null 2>&1; then
    print_success "Health check passed"
else
    print_error "Health check failed"
    print_status "Checking service logs..."
    docker-compose -f "$COMPOSE_FILE" logs api
    exit 1
fi

# Run database migrations
print_status "Running database migrations..."
if docker-compose -f "$COMPOSE_FILE" exec api npm run db:migrate; then
    print_success "Database migrations completed"
else
    print_warning "Database migrations failed or not needed"
fi

# Cleanup old images
print_status "Cleaning up old Docker images..."
docker image prune -f

print_success "Deployment to $ENVIRONMENT completed successfully!"
print_status "Application is running at: http://localhost:${PORT:-3001}"
print_status "Health check: http://localhost:${PORT:-3001}/api/health"

# Show running services
print_status "Running services:"
docker-compose -f "$COMPOSE_FILE" ps
