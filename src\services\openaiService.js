const openaiConfig = require('../config/openai');

class OpenAIService {
  constructor() {
    this.client = null;
    this.assistantId = null;
  }

  initialize() {
    try {
      this.client = openaiConfig.getClient();
      this.assistantId = openaiConfig.getConfig().assistantId;
      console.log('🤖 OpenAI Service initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize OpenAI Service:', error.message);
      throw error;
    }
  }

  async createAssistant(config = {}) {
    try {
      if (!this.client) {
        this.initialize();
      }

      const assistantConfig = {
        ...openaiConfig.getAssistantConfig(),
        ...config
      };

      const assistant = await this.client.beta.assistants.create(assistantConfig);
      
      console.log('✅ Assistant created:', assistant.id);
      return assistant;
    } catch (error) {
      console.error('❌ Failed to create assistant:', error.message);
      throw new Error(`Assistant creation failed: ${error.message}`);
    }
  }

  async createThread() {
    try {
      if (!this.client) {
        this.initialize();
      }

      const thread = await this.client.beta.threads.create();
      console.log('📝 Thread created:', thread.id);
      return thread;
    } catch (error) {
      console.error('❌ Failed to create thread:', error.message);
      throw new Error(`Thread creation failed: ${error.message}`);
    }
  }

  async sendMessage(threadId, message, excelData = null) {
    try {
      if (!this.client) {
        this.initialize();
      }

      const startTime = Date.now();

      // Prepare message content
      let messageContent = message;
      if (excelData) {
        messageContent += `\n\nExcel Data Context:\n${JSON.stringify(excelData, null, 2)}`;
      }

      // Add message to thread
      await this.client.beta.threads.messages.create(threadId, {
        role: 'user',
        content: messageContent
      });

      // Run the assistant
      const run = await this.client.beta.threads.runs.create(threadId, {
        assistant_id: this.assistantId || openaiConfig.getConfig().assistantId
      });

      // Wait for completion
      const completedRun = await this.waitForRunCompletion(threadId, run.id);
      
      // Get the response
      const messages = await this.client.beta.threads.messages.list(threadId);
      const assistantMessage = messages.data.find(msg => 
        msg.role === 'assistant' && 
        msg.run_id === completedRun.id
      );

      const processingTime = Date.now() - startTime;

      if (!assistantMessage) {
        throw new Error('No assistant response found');
      }

      const response = {
        content: assistantMessage.content[0].text.value,
        messageId: assistantMessage.id,
        runId: completedRun.id,
        processingTime,
        usage: completedRun.usage,
        functionCalls: this.extractFunctionCalls(completedRun)
      };

      console.log(`✅ Message processed in ${processingTime}ms`);
      return response;
    } catch (error) {
      console.error('❌ Failed to send message:', error.message);
      throw new Error(`Message sending failed: ${error.message}`);
    }
  }

  async waitForRunCompletion(threadId, runId, maxWaitTime = 60000) {
    const startTime = Date.now();
    const pollInterval = 1000; // 1 second

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const run = await this.client.beta.threads.runs.retrieve(threadId, runId);
        
        if (run.status === 'completed') {
          return run;
        }
        
        if (run.status === 'failed' || run.status === 'cancelled' || run.status === 'expired') {
          throw new Error(`Run ${run.status}: ${run.last_error?.message || 'Unknown error'}`);
        }

        if (run.status === 'requires_action') {
          // Handle function calls
          const updatedRun = await this.handleRequiredAction(threadId, runId, run);
          if (updatedRun.status === 'completed') {
            return updatedRun;
          }
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      } catch (error) {
        console.error('❌ Error polling run status:', error.message);
        throw error;
      }
    }

    throw new Error('Run completion timeout');
  }

  async handleRequiredAction(threadId, runId, run) {
    try {
      const toolCalls = run.required_action.submit_tool_outputs.tool_calls;
      const toolOutputs = [];

      for (const toolCall of toolCalls) {
        const functionName = toolCall.function.name;
        const functionArgs = JSON.parse(toolCall.function.arguments);
        
        console.log(`🔧 Executing function: ${functionName}`);
        
        const result = await this.handleFunctionCall(functionName, functionArgs);
        
        toolOutputs.push({
          tool_call_id: toolCall.id,
          output: JSON.stringify(result)
        });
      }

      // Submit tool outputs
      const updatedRun = await this.client.beta.threads.runs.submitToolOutputs(
        threadId,
        runId,
        { tool_outputs: toolOutputs }
      );

      // Wait for completion after submitting tool outputs
      return await this.waitForRunCompletion(threadId, updatedRun.id);
    } catch (error) {
      console.error('❌ Failed to handle required action:', error.message);
      throw error;
    }
  }

  async handleFunctionCall(functionName, parameters) {
    try {
      switch (functionName) {
        case 'analyze_excel_data':
          return await this.analyzeExcelData(parameters.data, parameters.analysis_type);
        
        case 'generate_excel_formula':
          return await this.generateExcelFormula(parameters.requirement, parameters.data_context);
        
        default:
          console.warn(`⚠️ Unknown function: ${functionName}`);
          return { error: `Unknown function: ${functionName}` };
      }
    } catch (error) {
      console.error(`❌ Function call error (${functionName}):`, error.message);
      return { error: error.message };
    }
  }

  async analyzeExcelData(data, analysisType) {
    // This would integrate with the Excel processor service
    const excelProcessor = require('./excelProcessor');
    return await excelProcessor.analyzeData(data, analysisType);
  }

  async generateExcelFormula(requirement, dataContext) {
    // This would integrate with the Excel processor service
    const excelProcessor = require('./excelProcessor');
    return await excelProcessor.generateFormula(requirement, dataContext);
  }

  extractFunctionCalls(run) {
    const functionCalls = [];
    
    if (run.required_action && run.required_action.submit_tool_outputs) {
      const toolCalls = run.required_action.submit_tool_outputs.tool_calls;
      
      for (const toolCall of toolCalls) {
        functionCalls.push({
          name: toolCall.function.name,
          parameters: JSON.parse(toolCall.function.arguments),
          id: toolCall.id
        });
      }
    }
    
    return functionCalls;
  }

  async getConversationHistory(threadId, limit = 20) {
    try {
      if (!this.client) {
        this.initialize();
      }

      const messages = await this.client.beta.threads.messages.list(threadId, {
        limit
      });

      return messages.data.map(message => ({
        id: message.id,
        role: message.role,
        content: message.content[0].text.value,
        timestamp: new Date(message.created_at * 1000),
        runId: message.run_id
      }));
    } catch (error) {
      console.error('❌ Failed to get conversation history:', error.message);
      throw new Error(`Failed to retrieve conversation history: ${error.message}`);
    }
  }

  async deleteThread(threadId) {
    try {
      if (!this.client) {
        this.initialize();
      }

      await this.client.beta.threads.del(threadId);
      console.log('🗑️ Thread deleted:', threadId);
      return true;
    } catch (error) {
      console.error('❌ Failed to delete thread:', error.message);
      throw new Error(`Thread deletion failed: ${error.message}`);
    }
  }

  async healthCheck() {
    try {
      if (!this.client) {
        this.initialize();
      }

      // Simple API call to check connectivity
      const models = await this.client.models.list();
      
      return {
        status: 'healthy',
        message: 'OpenAI service is operational',
        modelsAvailable: models.data.length,
        assistantId: this.assistantId
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        message: 'OpenAI service check failed',
        error: error.message
      };
    }
  }
}

// Create singleton instance
const openaiService = new OpenAIService();

module.exports = openaiService;
