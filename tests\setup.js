const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.MONGODB_TEST_URI = 'mongodb://localhost:27017/excel-chat-ai-test';
process.env.OPENAI_API_KEY = 'test-openai-key';
process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests

let mongoServer;

// Global test setup
beforeAll(async () => {
  // Start in-memory MongoDB instance
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  
  // Override the test database URI
  process.env.MONGODB_TEST_URI = mongoUri;
  
  // Connect to the in-memory database
  await mongoose.connect(mongo<PERSON>ri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  console.log('🧪 Test database connected');
});

// Global test teardown
afterAll(async () => {
  // Close database connection
  await mongoose.connection.close();
  
  // Stop the in-memory MongoDB instance
  if (mongoServer) {
    await mongoServer.stop();
  }
  
  console.log('🧪 Test database disconnected');
});

// Clean up database between tests
afterEach(async () => {
  // Clear all collections
  const collections = mongoose.connection.collections;
  
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

// Mock external services
jest.mock('../src/services/openaiService', () => ({
  initialize: jest.fn(),
  createThread: jest.fn().mockResolvedValue({ id: 'test-thread-id' }),
  sendMessage: jest.fn().mockResolvedValue({
    content: 'Test AI response',
    messageId: 'test-message-id',
    runId: 'test-run-id',
    processingTime: 1000,
    usage: { total_tokens: 100 },
    functionCalls: []
  }),
  deleteThread: jest.fn().mockResolvedValue(true),
  healthCheck: jest.fn().mockResolvedValue({
    status: 'healthy',
    message: 'OpenAI service is operational'
  })
}));

// Mock logger to reduce noise during tests
jest.mock('../src/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  },
  requestLogger: (req, res, next) => next(),
  performanceLogger: {
    start: jest.fn().mockReturnValue({
      end: jest.fn().mockReturnValue(100)
    })
  },
  securityLogger: {
    authFailure: jest.fn(),
    suspiciousActivity: jest.fn(),
    rateLimitExceeded: jest.fn()
  },
  businessLogger: {
    userAction: jest.fn(),
    systemEvent: jest.fn(),
    externalService: jest.fn()
  },
  healthLogger: {
    check: jest.fn()
  },
  logError: jest.fn()
}));

// Mock file system operations for testing
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  promises: {
    ...jest.requireActual('fs').promises,
    readFile: jest.fn(),
    writeFile: jest.fn(),
    unlink: jest.fn(),
    mkdir: jest.fn(),
    access: jest.fn()
  }
}));

// Global test utilities
global.testUtils = {
  // Create test user
  createTestUser: async (userData = {}) => {
    const User = require('../src/models/User');
    const defaultUser = {
      email: '<EMAIL>',
      password: 'TestPassword123',
      firstName: 'Test',
      lastName: 'User',
      ...userData
    };
    
    const user = new User(defaultUser);
    await user.save();
    return user;
  },

  // Create test conversation
  createTestConversation: async (userId, conversationData = {}) => {
    const Conversation = require('../src/models/Conversation');
    const defaultConversation = {
      userId,
      threadId: 'test-thread-id',
      title: 'Test Conversation',
      description: 'Test conversation description',
      ...conversationData
    };
    
    const conversation = new Conversation(defaultConversation);
    await conversation.save();
    return conversation;
  },

  // Create test Excel session
  createTestExcelSession: async (userId, sessionData = {}) => {
    const ExcelSession = require('../src/models/ExcelSession');
    const defaultSession = {
      userId,
      sessionId: 'test-session-id',
      fileName: 'test-file.xlsx',
      originalFileName: 'test-file.xlsx',
      filePath: '/tmp/test-file.xlsx',
      fileSize: 1024,
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      status: 'ready',
      ...sessionData
    };
    
    const session = new ExcelSession(defaultSession);
    await session.save();
    return session;
  },

  // Generate test JWT token
  generateTestToken: (userId, role = 'user') => {
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      { userId, role, email: '<EMAIL>' },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
  },

  // Create test file buffer
  createTestFileBuffer: (content = 'test file content') => {
    return Buffer.from(content);
  },

  // Mock request object
  mockRequest: (overrides = {}) => ({
    body: {},
    params: {},
    query: {},
    headers: {},
    user: null,
    ip: '127.0.0.1',
    method: 'GET',
    originalUrl: '/test',
    get: jest.fn(),
    ...overrides
  }),

  // Mock response object
  mockResponse: () => {
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      end: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      clearCookie: jest.fn().mockReturnThis()
    };
    return res;
  },

  // Mock next function
  mockNext: () => jest.fn(),

  // Wait for async operations
  wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms))
};

// Increase timeout for async operations
jest.setTimeout(30000);
