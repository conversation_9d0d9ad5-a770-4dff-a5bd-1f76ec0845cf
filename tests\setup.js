// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.MONGODB_TEST_URI = 'mongodb://localhost:27017/excel-chat-ai-test';
process.env.OPENAI_API_KEY = 'test-openai-key';
process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests

// Mock mongoose for testing
jest.mock('mongoose', () => ({
  connect: jest.fn().mockResolvedValue({}),
  connection: {
    close: jest.fn().mockResolvedValue({}),
    collections: {},
    readyState: 1,
    host: 'localhost',
    port: 27017,
    name: 'test-db',
    db: {
      admin: () => ({
        ping: jest.fn().mockResolvedValue({ ok: 1 })
      })
    }
  },
  Schema: jest.fn().mockImplementation(() => ({
    pre: jest.fn(),
    methods: {},
    statics: {},
    virtual: jest.fn().mockReturnValue({
      get: jest.fn()
    }),
    index: jest.fn()
  })),
  model: jest.fn().mockImplementation((name, schema) => {
    const MockModel = function(data) {
      Object.assign(this, data);
      this._id = 'mock-id-' + Date.now();
      this.save = jest.fn().mockResolvedValue(this);
      this.toJSON = jest.fn().mockReturnValue(data);
    };

    MockModel.find = jest.fn().mockReturnValue({
      sort: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      populate: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue([])
    });

    MockModel.findOne = jest.fn().mockResolvedValue(null);
    MockModel.findById = jest.fn().mockResolvedValue(null);
    MockModel.findByIdAndUpdate = jest.fn().mockResolvedValue(null);
    MockModel.findByIdAndDelete = jest.fn().mockResolvedValue(null);
    MockModel.deleteOne = jest.fn().mockResolvedValue({ deletedCount: 1 });
    MockModel.deleteMany = jest.fn().mockResolvedValue({ deletedCount: 0 });
    MockModel.create = jest.fn().mockImplementation((data) => new MockModel(data));
    MockModel.createIndexes = jest.fn().mockResolvedValue({});

    return MockModel;
  })
}));

// Global test setup
beforeAll(async () => {
  console.log('🧪 Test environment initialized');
});

// Global test teardown
afterAll(async () => {
  console.log('🧪 Test environment cleaned up');
});

// Clean up between tests
afterEach(async () => {
  jest.clearAllMocks();
});

// Mock external services
jest.mock('../src/services/openaiService', () => ({
  initialize: jest.fn(),
  createThread: jest.fn().mockResolvedValue({ id: 'test-thread-id' }),
  sendMessage: jest.fn().mockResolvedValue({
    content: 'Test AI response',
    messageId: 'test-message-id',
    runId: 'test-run-id',
    processingTime: 1000,
    usage: { total_tokens: 100 },
    functionCalls: []
  }),
  deleteThread: jest.fn().mockResolvedValue(true),
  healthCheck: jest.fn().mockResolvedValue({
    status: 'healthy',
    message: 'OpenAI service is operational'
  })
}));

// Mock logger to reduce noise during tests
jest.mock('../src/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  },
  requestLogger: (req, res, next) => next(),
  performanceLogger: {
    start: jest.fn().mockReturnValue({
      end: jest.fn().mockReturnValue(100)
    })
  },
  securityLogger: {
    authFailure: jest.fn(),
    suspiciousActivity: jest.fn(),
    rateLimitExceeded: jest.fn()
  },
  businessLogger: {
    userAction: jest.fn(),
    systemEvent: jest.fn(),
    externalService: jest.fn()
  },
  healthLogger: {
    check: jest.fn()
  },
  logError: jest.fn()
}));

// Mock file system operations for testing
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  promises: {
    ...jest.requireActual('fs').promises,
    readFile: jest.fn(),
    writeFile: jest.fn(),
    unlink: jest.fn(),
    mkdir: jest.fn(),
    access: jest.fn()
  }
}));

// Global test utilities
global.testUtils = {
  // Create test user
  createTestUser: async (userData = {}) => {
    const defaultUser = {
      _id: 'mock-user-id-' + Date.now(),
      email: '<EMAIL>',
      password: 'TestPassword123',
      firstName: 'Test',
      lastName: 'User',
      role: 'user',
      isActive: true,
      isEmailVerified: false,
      preferences: {
        theme: 'auto',
        language: 'en',
        notifications: { email: true, push: true }
      },
      subscription: { plan: 'free', isActive: false },
      usage: { totalSessions: 0, totalMessages: 0, totalFilesUploaded: 0 },
      createdAt: new Date(),
      updatedAt: new Date(),
      ...userData
    };

    // Mock user methods
    defaultUser.save = jest.fn().mockResolvedValue(defaultUser);
    defaultUser.comparePassword = jest.fn().mockResolvedValue(true);
    defaultUser.generateAuthToken = jest.fn().mockReturnValue('mock-jwt-token');
    defaultUser.updateLastLogin = jest.fn().mockResolvedValue(defaultUser);
    defaultUser.incrementUsage = jest.fn().mockResolvedValue(defaultUser);
    defaultUser.toJSON = jest.fn().mockReturnValue(defaultUser);

    // Add virtual properties
    Object.defineProperty(defaultUser, 'fullName', {
      get: function() { return `${this.firstName} ${this.lastName}`; }
    });

    return defaultUser;
  },

  // Create test conversation
  createTestConversation: async (userId, conversationData = {}) => {
    const defaultConversation = {
      _id: 'mock-conversation-id-' + Date.now(),
      userId,
      threadId: 'test-thread-id',
      title: 'Test Conversation',
      description: 'Test conversation description',
      status: 'active',
      messages: [],
      excelContext: { hasExcelData: false },
      analytics: { totalMessages: 0, totalTokens: 0 },
      tags: [],
      isStarred: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...conversationData
    };

    // Mock conversation methods
    defaultConversation.save = jest.fn().mockResolvedValue(defaultConversation);
    defaultConversation.addMessage = jest.fn().mockResolvedValue(defaultConversation);
    defaultConversation.updateExcelContext = jest.fn().mockResolvedValue(defaultConversation);

    return defaultConversation;
  },

  // Create test Excel session
  createTestExcelSession: async (userId, sessionData = {}) => {
    const defaultSession = {
      _id: 'mock-session-id-' + Date.now(),
      userId,
      sessionId: 'test-session-id',
      fileName: 'test-file.xlsx',
      originalFileName: 'test-file.xlsx',
      filePath: '/tmp/test-file.xlsx',
      fileSize: 1024,
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      status: 'ready',
      workbook: { sheetCount: 1, sheets: [] },
      createdAt: new Date(),
      updatedAt: new Date(),
      ...sessionData
    };

    // Mock session methods
    defaultSession.save = jest.fn().mockResolvedValue(defaultSession);
    defaultSession.startProcessing = jest.fn().mockResolvedValue(defaultSession);
    defaultSession.completeProcessing = jest.fn().mockResolvedValue(defaultSession);
    defaultSession.updateLastAccessed = jest.fn().mockResolvedValue(defaultSession);

    return defaultSession;
  },

  // Generate test JWT token
  generateTestToken: (userId, role = 'user') => {
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      { userId, role, email: '<EMAIL>' },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
  },

  // Create test file buffer
  createTestFileBuffer: (content = 'test file content') => {
    return Buffer.from(content);
  },

  // Mock request object
  mockRequest: (overrides = {}) => ({
    body: {},
    params: {},
    query: {},
    headers: {},
    user: null,
    ip: '127.0.0.1',
    method: 'GET',
    originalUrl: '/test',
    get: jest.fn(),
    ...overrides
  }),

  // Mock response object
  mockResponse: () => {
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      end: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      clearCookie: jest.fn().mockReturnThis()
    };
    return res;
  },

  // Mock next function
  mockNext: () => jest.fn(),

  // Wait for async operations
  wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms))
};

// Increase timeout for async operations
jest.setTimeout(30000);
