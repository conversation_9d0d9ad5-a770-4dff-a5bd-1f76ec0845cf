const { validationResult } = require('express-validator');
const Joi = require('joi');

// Express-validator middleware
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }));

    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: formattedErrors
    });
  }

  next();
};

// Joi validation schemas
const schemas = {
  // User registration schema
  userRegistration: Joi.object({
    email: Joi.string()
      .email()
      .required()
      .messages({
        'string.email': 'Please provide a valid email address',
        'any.required': 'Email is required'
      }),
    password: Joi.string()
      .min(6)
      .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .required()
      .messages({
        'string.min': 'Password must be at least 6 characters long',
        'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, and one number',
        'any.required': 'Password is required'
      }),
    firstName: Joi.string()
      .trim()
      .min(1)
      .max(50)
      .required()
      .messages({
        'string.min': 'First name is required',
        'string.max': 'First name cannot exceed 50 characters',
        'any.required': 'First name is required'
      }),
    lastName: Joi.string()
      .trim()
      .min(1)
      .max(50)
      .required()
      .messages({
        'string.min': 'Last name is required',
        'string.max': 'Last name cannot exceed 50 characters',
        'any.required': 'Last name is required'
      })
  }),

  // User login schema
  userLogin: Joi.object({
    email: Joi.string()
      .email()
      .required()
      .messages({
        'string.email': 'Please provide a valid email address',
        'any.required': 'Email is required'
      }),
    password: Joi.string()
      .required()
      .messages({
        'any.required': 'Password is required'
      })
  }),

  // Chat message schema
  chatMessage: Joi.object({
    message: Joi.string()
      .trim()
      .min(1)
      .max(2000)
      .required()
      .messages({
        'string.min': 'Message cannot be empty',
        'string.max': 'Message cannot exceed 2000 characters',
        'any.required': 'Message is required'
      }),
    excelData: Joi.object().optional(),
    threadId: Joi.string().optional()
  }),

  // Excel analysis schema
  excelAnalysis: Joi.object({
    data: Joi.object()
      .required()
      .messages({
        'any.required': 'Data is required for analysis'
      }),
    analysisType: Joi.string()
      .valid('structure', 'statistics', 'patterns', 'quality')
      .default('structure')
      .messages({
        'any.only': 'Analysis type must be one of: structure, statistics, patterns, quality'
      })
  }),

  // Formula generation schema
  formulaGeneration: Joi.object({
    requirement: Joi.string()
      .trim()
      .min(1)
      .max(1000)
      .required()
      .messages({
        'string.min': 'Requirement cannot be empty',
        'string.max': 'Requirement cannot exceed 1000 characters',
        'any.required': 'Requirement is required'
      }),
    dataContext: Joi.object().optional()
  }),

  // Feedback schema
  feedback: Joi.object({
    conversationId: Joi.string()
      .required()
      .messages({
        'any.required': 'Conversation ID is required'
      }),
    messageId: Joi.string().optional(),
    rating: Joi.number()
      .integer()
      .min(1)
      .max(5)
      .optional()
      .messages({
        'number.min': 'Rating must be between 1 and 5',
        'number.max': 'Rating must be between 1 and 5'
      }),
    feedback: Joi.string()
      .trim()
      .max(1000)
      .optional()
      .messages({
        'string.max': 'Feedback cannot exceed 1000 characters'
      }),
    type: Joi.string()
      .valid('helpful', 'not_helpful', 'incorrect', 'inappropriate', 'other')
      .optional()
      .messages({
        'any.only': 'Feedback type must be one of: helpful, not_helpful, incorrect, inappropriate, other'
      })
  }),

  // Profile update schema
  profileUpdate: Joi.object({
    firstName: Joi.string()
      .trim()
      .min(1)
      .max(50)
      .optional()
      .messages({
        'string.min': 'First name cannot be empty',
        'string.max': 'First name cannot exceed 50 characters'
      }),
    lastName: Joi.string()
      .trim()
      .min(1)
      .max(50)
      .optional()
      .messages({
        'string.min': 'Last name cannot be empty',
        'string.max': 'Last name cannot exceed 50 characters'
      }),
    preferences: Joi.object({
      theme: Joi.string()
        .valid('light', 'dark', 'auto')
        .optional()
        .messages({
          'any.only': 'Theme must be light, dark, or auto'
        }),
      language: Joi.string()
        .min(2)
        .max(5)
        .optional()
        .messages({
          'string.min': 'Language code must be at least 2 characters',
          'string.max': 'Language code cannot exceed 5 characters'
        }),
      notifications: Joi.object({
        email: Joi.boolean().optional(),
        push: Joi.boolean().optional()
      }).optional()
    }).optional()
  })
};

// Joi validation middleware factory
const validateJoi = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const formattedErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: formattedErrors
      });
    }

    // Replace req.body with validated and sanitized data
    req.body = value;
    next();
  };
};

// Sanitization middleware
const sanitizeInput = (req, res, next) => {
  // Recursively sanitize strings in request body
  const sanitize = (obj) => {
    if (typeof obj === 'string') {
      return obj.trim();
    } else if (Array.isArray(obj)) {
      return obj.map(sanitize);
    } else if (obj && typeof obj === 'object') {
      const sanitized = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitize(value);
      }
      return sanitized;
    }
    return obj;
  };

  if (req.body) {
    req.body = sanitize(req.body);
  }

  if (req.query) {
    req.query = sanitize(req.query);
  }

  if (req.params) {
    req.params = sanitize(req.params);
  }

  next();
};

// File validation middleware
const validateFile = (options = {}) => {
  const {
    allowedTypes = ['.xlsx', '.xls', '.csv'],
    maxSize = 10 * 1024 * 1024, // 10MB
    required = true
  } = options;

  return (req, res, next) => {
    if (!req.file && required) {
      return res.status(400).json({
        success: false,
        message: 'File is required'
      });
    }

    if (req.file) {
      const fileExt = require('path').extname(req.file.originalname).toLowerCase();
      
      if (!allowedTypes.includes(fileExt)) {
        return res.status(400).json({
          success: false,
          message: `File type ${fileExt} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
        });
      }

      if (req.file.size > maxSize) {
        return res.status(400).json({
          success: false,
          message: `File size exceeds maximum allowed size of ${maxSize / 1024 / 1024}MB`
        });
      }
    }

    next();
  };
};

// Query parameter validation
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });

    if (error) {
      const formattedErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      return res.status(400).json({
        success: false,
        message: 'Query validation failed',
        errors: formattedErrors
      });
    }

    req.query = value;
    next();
  };
};

module.exports = {
  validateRequest,
  validateJoi,
  validateFile,
  validateQuery,
  sanitizeInput,
  schemas
};
