const rateLimit = require('express-rate-limit');
const config = require('../config/environment');

// General API rate limiter
const generalLimiter = rateLimit({
  windowMs: config.security.rateLimitWindowMs, // 15 minutes
  max: config.security.rateLimitMaxRequests, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil(config.security.rateLimitWindowMs / 1000 / 60) // minutes
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  handler: (req, res) => {
    console.warn(`Rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.ceil(config.security.rateLimitWindowMs / 1000 / 60)
    });
  }
});

// Strict rate limiter for authentication endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs for auth endpoints
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.',
    retryAfter: 15
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
  handler: (req, res) => {
    console.warn(`Auth rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Too many authentication attempts, please try again later.',
      retryAfter: 15
    });
  }
});

// Chat message rate limiter
const chatLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 20, // limit each IP to 20 chat messages per minute
  message: {
    success: false,
    message: 'Too many chat messages, please slow down.',
    retryAfter: 1
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use user ID if authenticated, otherwise fall back to IP
    return req.user?.userId || req.ip;
  },
  handler: (req, res) => {
    console.warn(`Chat rate limit exceeded for user: ${req.user?.userId || req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Too many chat messages, please slow down.',
      retryAfter: 1
    });
  }
});

// File upload rate limiter
const uploadLimiter = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 5, // limit each user to 5 file uploads per 10 minutes
  message: {
    success: false,
    message: 'Too many file uploads, please try again later.',
    retryAfter: 10
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return req.user?.userId || req.ip;
  },
  handler: (req, res) => {
    console.warn(`Upload rate limit exceeded for user: ${req.user?.userId || req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Too many file uploads, please try again later.',
      retryAfter: 10
    });
  }
});

// API analysis rate limiter
const analysisLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // limit each user to 10 analysis requests per 5 minutes
  message: {
    success: false,
    message: 'Too many analysis requests, please try again later.',
    retryAfter: 5
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return req.user?.userId || req.ip;
  },
  handler: (req, res) => {
    console.warn(`Analysis rate limit exceeded for user: ${req.user?.userId || req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Too many analysis requests, please try again later.',
      retryAfter: 5
    });
  }
});

// Dynamic rate limiter based on user subscription
const createSubscriptionBasedLimiter = (limits) => {
  return rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: (req) => {
      const userPlan = req.user?.subscription?.plan || 'free';
      return limits[userPlan] || limits.free;
    },
    message: (req) => {
      const userPlan = req.user?.subscription?.plan || 'free';
      return {
        success: false,
        message: `Rate limit exceeded for ${userPlan} plan. Consider upgrading for higher limits.`,
        currentPlan: userPlan,
        retryAfter: 1
      };
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      return req.user?.userId || req.ip;
    },
    handler: (req, res) => {
      const userPlan = req.user?.subscription?.plan || 'free';
      console.warn(`Subscription rate limit exceeded for user: ${req.user?.userId || req.ip}, plan: ${userPlan}`);
      
      res.status(429).json({
        success: false,
        message: `Rate limit exceeded for ${userPlan} plan. Consider upgrading for higher limits.`,
        currentPlan: userPlan,
        retryAfter: 1
      });
    }
  });
};

// Subscription-based chat limiter
const chatSubscriptionLimiter = createSubscriptionBasedLimiter({
  free: 10,      // 10 messages per minute
  basic: 30,     // 30 messages per minute
  premium: 60,   // 60 messages per minute
  enterprise: 120 // 120 messages per minute
});

// Subscription-based analysis limiter
const analysisSubscriptionLimiter = createSubscriptionBasedLimiter({
  free: 3,       // 3 analysis per minute
  basic: 10,     // 10 analysis per minute
  premium: 20,   // 20 analysis per minute
  enterprise: 50 // 50 analysis per minute
});

// Progressive rate limiter that increases limits for trusted users
const createProgressiveLimiter = (baseLimit, windowMs = 60000) => {
  return rateLimit({
    windowMs,
    max: (req) => {
      // Increase limits for users with good history
      const user = req.user;
      if (!user) return baseLimit;

      let multiplier = 1;
      
      // Increase limits based on account age
      const accountAge = Date.now() - new Date(user.createdAt).getTime();
      const daysOld = accountAge / (1000 * 60 * 60 * 24);
      
      if (daysOld > 30) multiplier += 0.5;  // 50% more for 30+ day accounts
      if (daysOld > 90) multiplier += 0.5;  // 100% more for 90+ day accounts
      
      // Increase limits for premium users
      if (user.subscription?.plan === 'premium') multiplier += 1;
      if (user.subscription?.plan === 'enterprise') multiplier += 2;

      return Math.floor(baseLimit * multiplier);
    },
    keyGenerator: (req) => {
      return req.user?.userId || req.ip;
    },
    standardHeaders: true,
    legacyHeaders: false
  });
};

// Burst limiter for handling short bursts of requests
const burstLimiter = rateLimit({
  windowMs: 1000, // 1 second
  max: 5, // 5 requests per second
  message: {
    success: false,
    message: 'Too many requests in a short time, please slow down.',
    retryAfter: 1
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    console.warn(`Burst rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Too many requests in a short time, please slow down.',
      retryAfter: 1
    });
  }
});

// Skip rate limiting for certain conditions
const skipRateLimit = (req) => {
  // Skip rate limiting for health checks
  if (req.path === '/api/health') return true;
  
  // Skip for admin users in development
  if (config.server.nodeEnv === 'development' && req.user?.role === 'admin') return true;
  
  return false;
};

// Apply skip logic to all limiters
[generalLimiter, authLimiter, chatLimiter, uploadLimiter, analysisLimiter].forEach(limiter => {
  limiter.skip = skipRateLimit;
});

module.exports = {
  generalLimiter,
  authLimiter,
  chatLimiter,
  uploadLimiter,
  analysisLimiter,
  chatSubscriptionLimiter,
  analysisSubscriptionLimiter,
  createSubscriptionBasedLimiter,
  createProgressiveLimiter,
  burstLimiter
};
