#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (query) => new Promise((resolve) => rl.question(query, resolve));

async function setupProject() {
  console.log('🚀 Excel Chat AI Backend Setup');
  console.log('================================\n');

  try {
    // Check if .env file exists
    const envPath = path.join(process.cwd(), '.env');
    const envExamplePath = path.join(process.cwd(), '.env.example');

    let envExists = false;
    try {
      await fs.access(envPath);
      envExists = true;
    } catch (error) {
      // .env doesn't exist
    }

    if (envExists) {
      const overwrite = await question('⚠️  .env file already exists. Overwrite? (y/N): ');
      if (overwrite.toLowerCase() !== 'y') {
        console.log('Setup cancelled.');
        rl.close();
        return;
      }
    }

    console.log('📝 Setting up environment configuration...\n');

    // Read .env.example
    const envExample = await fs.readFile(envExamplePath, 'utf8');
    let envContent = envExample;

    // Collect user inputs
    console.log('Please provide the following configuration values:\n');

    // MongoDB URI
    const mongoUri = await question('MongoDB URI (press Enter for default): ');
    if (mongoUri.trim()) {
      envContent = envContent.replace(
        /MONGODB_URI=.*/,
        `MONGODB_URI=${mongoUri.trim()}`
      );
    }

    // OpenAI API Key
    const openaiKey = await question('OpenAI API Key (required): ');
    if (openaiKey.trim()) {
      envContent = envContent.replace(
        /OPENAI_API_KEY=.*/,
        `OPENAI_API_KEY=${openaiKey.trim()}`
      );
    } else {
      console.log('⚠️  OpenAI API Key is required for the application to work properly.');
    }

    // OpenAI Assistant ID (optional)
    const assistantId = await question('OpenAI Assistant ID (optional): ');
    if (assistantId.trim()) {
      envContent = envContent.replace(
        /OPENAI_ASSISTANT_ID=.*/,
        `OPENAI_ASSISTANT_ID=${assistantId.trim()}`
      );
    }

    // JWT Secret
    const jwtSecret = await question('JWT Secret (press Enter to generate): ');
    if (jwtSecret.trim()) {
      envContent = envContent.replace(
        /JWT_SECRET=.*/,
        `JWT_SECRET=${jwtSecret.trim()}`
      );
    } else {
      const crypto = require('crypto');
      const generatedSecret = crypto.randomBytes(64).toString('hex');
      envContent = envContent.replace(
        /JWT_SECRET=.*/,
        `JWT_SECRET=${generatedSecret}`
      );
      console.log('✅ Generated JWT secret automatically.');
    }

    // Server Port
    const port = await question('Server Port (default: 3001): ');
    if (port.trim()) {
      envContent = envContent.replace(
        /PORT=.*/,
        `PORT=${port.trim()}`
      );
    }

    // CORS Origins
    const corsOrigins = await question('CORS Origins (comma-separated, default: localhost:3000): ');
    if (corsOrigins.trim()) {
      envContent = envContent.replace(
        /CORS_ORIGINS=.*/,
        `CORS_ORIGINS=${corsOrigins.trim()}`
      );
    }

    // Write .env file
    await fs.writeFile(envPath, envContent);
    console.log('\n✅ .env file created successfully!');

    // Create required directories
    console.log('\n📁 Creating required directories...');
    const directories = [
      'logs',
      'uploads',
      'temp'
    ];

    for (const dir of directories) {
      try {
        await fs.mkdir(dir, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
      } catch (error) {
        console.log(`⚠️  Directory ${dir} already exists or couldn't be created.`);
      }
    }

    // Set up log files
    console.log('\n📝 Setting up log files...');
    const logFiles = [
      'logs/app.log',
      'logs/error.log',
      'logs/api.log',
      'logs/exceptions.log',
      'logs/rejections.log'
    ];

    for (const logFile of logFiles) {
      try {
        await fs.writeFile(logFile, '');
        console.log(`✅ Created log file: ${logFile}`);
      } catch (error) {
        console.log(`⚠️  Couldn't create log file: ${logFile}`);
      }
    }

    // Check Node.js version
    console.log('\n🔍 Checking Node.js version...');
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion >= 18) {
      console.log(`✅ Node.js version ${nodeVersion} is supported.`);
    } else {
      console.log(`⚠️  Node.js version ${nodeVersion} detected. Version 18+ is recommended.`);
    }

    // Installation instructions
    console.log('\n🎉 Setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Install dependencies: npm install');
    console.log('2. Run database migrations: npm run db:migrate');
    console.log('3. Start development server: npm run dev');
    console.log('4. Check health: http://localhost:3001/api/health');
    console.log('\nFor production deployment:');
    console.log('1. Set NODE_ENV=production in .env');
    console.log('2. Build the application: npm run build');
    console.log('3. Start production server: npm start');

    // Validate configuration
    console.log('\n🔍 Validating configuration...');
    try {
      require('dotenv').config({ path: envPath });
      const config = require('../src/config/environment');
      console.log('✅ Configuration validation passed.');
    } catch (error) {
      console.log('⚠️  Configuration validation failed:', error.message);
      console.log('Please check your .env file and try again.');
    }

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run setup if called directly
if (require.main === module) {
  setupProject();
}

module.exports = { setupProject };
