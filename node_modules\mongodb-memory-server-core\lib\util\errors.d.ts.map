{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../src/util/errors.ts"], "names": [], "mappings": "AAEA,qBAAa,UAAW,SAAQ,KAAK;IAChB,YAAY,EAAE,MAAM,EAAE;IAAS,QAAQ,EAAE,MAAM;gBAA/C,YAAY,EAAE,MAAM,EAAE,EAAS,QAAQ,EAAE,MAAM;CASnE;AAED,qBAAa,0BAA2B,SAAQ,KAAK;IAChC,MAAM,EAAE,MAAM;gBAAd,MAAM,EAAE,MAAM;CAGlC;AAED,qBAAa,2BAA4B,SAAQ,KAAK;IACjC,YAAY,EAAE,OAAO;IAAS,IAAI,EAAE,MAAM;gBAA1C,YAAY,EAAE,OAAO,EAAS,IAAI,EAAE,MAAM;CAO9D;AAED,qBAAa,oBAAqB,SAAQ,KAAK;IAC1B,QAAQ,EAAE,MAAM;gBAAhB,QAAQ,EAAE,MAAM;CAGpC;AAED,qBAAa,wBAAyB,SAAQ,KAAK;IAC9B,IAAI,EAAE,MAAM;IAAS,QAAQ,CAAC;gBAA9B,IAAI,EAAE,MAAM,EAAS,QAAQ,CAAC,oBAAQ;CAS1D;AAED,qBAAa,0BAA2B,SAAQ,KAAK;IAChC,OAAO,EAAE,MAAM;IAAS,KAAK,CAAC;gBAA9B,OAAO,EAAE,MAAM,EAAS,KAAK,CAAC,oBAAQ;CAG1D;AAGD,qBAAa,mBAAoB,SAAQ,KAAK;IACzB,SAAS,EAAE,OAAO;gBAAlB,SAAS,EAAE,OAAO;CAUtC;AAGD,qBAAa,wBAAyB,SAAQ,KAAK;IAC9B,UAAU,EAAE,MAAM;gBAAlB,UAAU,EAAE,MAAM;CAKtC;AAED,qBAAa,mBAAoB,SAAQ,KAAK;IACzB,SAAS,EAAE,MAAM;IAAS,YAAY,EAAE,MAAM;gBAA9C,SAAS,EAAE,MAAM,EAAS,YAAY,EAAE,MAAM;CAGlE;AAED,qBAAa,sBAAuB,SAAQ,KAAK;IAC5B,MAAM,EAAE,MAAM;gBAAd,MAAM,EAAE,MAAM;CAGlC;AAED,qBAAa,iBAAkB,SAAQ,KAAK;IACvB,KAAK,EAAE,MAAM;gBAAb,KAAK,EAAE,MAAM;CAGjC;AAED,qBAAa,mBAAoB,SAAQ,KAAK;;CAI7C;AAED,qBAAa,kBAAmB,SAAQ,KAAK;;CAI5C;AAED,qBAAa,4BAA6B,SAAQ,KAAK;IAClC,IAAI,EAAE,MAAM;gBAAZ,IAAI,EAAE,MAAM;CAGhC;AAED,qBAAa,mBAAoB,SAAQ,KAAK;IACzB,IAAI,EAAE,MAAM;gBAAZ,IAAI,EAAE,MAAM;CAGhC;AAED;;GAEG;AACH,qBAAa,sBAAuB,SAAQ,KAAK;;CAIhD;AAED,qBAAa,oBAAqB,SAAQ,KAAK;IAC1B,KAAK,EAAE,MAAM;gBAAb,KAAK,EAAE,MAAM;CAGjC;AAED,qBAAa,sBAAuB,SAAQ,KAAK;IAC5B,GAAG,EAAE,MAAM;gBAAX,GAAG,EAAE,MAAM;CAG/B;AAED,qBAAa,iBAAkB,SAAQ,KAAK;IACvB,IAAI,EAAE,MAAM;IAAS,KAAK,CAAC;gBAA3B,IAAI,EAAE,MAAM,EAAS,KAAK,CAAC,oBAAQ;CAKvD;AAED,qBAAa,gCAAiC,SAAQ,KAAK;IAEhD,IAAI,EAAE,MAAM;IACZ,iBAAiB,EAAE,MAAM;IACzB,kBAAkB,EAAE,MAAM;IAC1B,KAAK,CAAC;gBAHN,IAAI,EAAE,MAAM,EACZ,iBAAiB,EAAE,MAAM,EACzB,kBAAkB,EAAE,MAAM,EAC1B,KAAK,CAAC,oBAAQ;CAOxB;AAED;;GAEG;AACH,qBAAa,mBAAoB,SAAQ,KAAK;gBAEhC,GAAG,EAAE,MAAM;CAGxB;AAED;;GAEG;AACH,qBAAa,oBAAqB,SAAQ,KAAK;gBACjC,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;CAevD;AAED;;GAEG;AACH,qBAAa,mBAAoB,SAAQ,KAAK;IACzB,OAAO,EAAE,MAAM;gBAAf,OAAO,EAAE,MAAM;CAGnC;AAED;;GAEG;AACH,qBAAa,aAAc,SAAQ,KAAK;IACnB,GAAG,EAAE,MAAM;IAAS,GAAG,EAAE,MAAM;gBAA/B,GAAG,EAAE,MAAM,EAAS,GAAG,EAAE,MAAM;CAGnD;AAGD,qBAAa,eAAgB,SAAQ,KAAK;CAAG"}