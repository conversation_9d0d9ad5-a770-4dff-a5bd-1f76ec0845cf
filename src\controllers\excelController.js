const ExcelSession = require('../models/ExcelSession');
const User = require('../models/User');
const excelProcessor = require('../services/excelProcessor');
const dataOptimizer = require('../services/dataOptimizer');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

class ExcelController {
  // Analyze Excel data
  async analyzeData(req, res) {
    try {
      const { data, analysisType = 'structure' } = req.body;
      const userId = req.user.userId;

      console.log(`🔍 Analyzing Excel data for user ${userId}, type: ${analysisType}`);

      if (!data) {
        return res.status(400).json({
          success: false,
          message: 'Excel data is required'
        });
      }

      const analysis = await excelProcessor.analyzeData(data, analysisType);

      res.status(200).json({
        success: true,
        message: 'Data analysis completed successfully',
        data: {
          analysis,
          timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('❌ Data analysis error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to analyze data',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Generate Excel formula
  async generateFormula(req, res) {
    try {
      const { requirement, dataContext } = req.body;
      const userId = req.user.userId;

      console.log(`🧮 Generating formula for user ${userId}: ${requirement}`);

      if (!requirement) {
        return res.status(400).json({
          success: false,
          message: 'Formula requirement is required'
        });
      }

      const formula = await excelProcessor.generateFormula(requirement, dataContext);

      res.status(200).json({
        success: true,
        message: 'Formula generated successfully',
        data: {
          formula,
          timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('❌ Formula generation error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to generate formula',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Upload Excel file
  async uploadFile(req, res) {
    try {
      const userId = req.user.userId;
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          message: 'No file uploaded'
        });
      }

      console.log(`📁 Processing uploaded file: ${file.originalname}`);

      // Generate session ID
      const sessionId = uuidv4();

      // Create Excel session
      const excelSession = new ExcelSession({
        userId,
        sessionId,
        fileName: `${sessionId}_${file.originalname}`,
        originalFileName: file.originalname,
        filePath: file.path,
        fileSize: file.size,
        mimeType: file.mimetype,
        status: 'processing'
      });

      await excelSession.save();
      await excelSession.startProcessing();

      try {
        // Process the file
        const fileBuffer = await fs.readFile(file.path);
        const processedData = await excelProcessor.processFile(fileBuffer, {
          fileName: file.originalname,
          includeFormulas: true,
          maxRows: 10000,
          detectDataTypes: true
        });

        // Update session with processed data
        excelSession.workbook = {
          sheetCount: processedData.sheetCount,
          sheets: processedData.sheets.map(sheet => ({
            name: sheet.name,
            index: sheet.index,
            rowCount: sheet.rowCount,
            columnCount: sheet.columnCount,
            hasHeaders: sheet.hasHeaders,
            dataTypes: sheet.columns ? sheet.columns.map(col => ({
              column: col.name,
              type: col.type,
              samples: col.samples || []
            })) : [],
            statistics: sheet.statistics
          })),
          metadata: processedData.metadata
        };

        // Perform automatic analysis if enabled
        if (excelSession.settings.autoAnalyze) {
          const analysis = await excelProcessor.analyzeData(processedData, 'structure');
          await excelSession.addAnalysis({
            type: 'structure',
            result: analysis.result,
            timestamp: new Date(),
            processingTime: 0
          });
        }

        await excelSession.completeProcessing(true);

        // Update user usage
        const user = await User.findById(userId);
        if (user) {
          await user.incrementUsage('file');
        }

        res.status(200).json({
          success: true,
          message: 'File uploaded and processed successfully',
          data: {
            sessionId: excelSession.sessionId,
            fileName: excelSession.originalFileName,
            fileSize: excelSession.fileSize,
            sheetCount: processedData.sheetCount,
            sheets: processedData.sheets.map(sheet => ({
              name: sheet.name,
              rowCount: sheet.rowCount,
              columnCount: sheet.columnCount,
              hasHeaders: sheet.hasHeaders
            })),
            processingTime: processedData.processingTime,
            warnings: processedData.warnings || []
          }
        });
      } catch (processingError) {
        console.error('❌ File processing error:', processingError.message);
        
        // Update session with error
        excelSession.processing.errors.push({
          message: processingError.message,
          stack: processingError.stack,
          timestamp: new Date()
        });
        
        await excelSession.completeProcessing(false);

        res.status(500).json({
          success: false,
          message: 'File processing failed',
          error: processingError.message
        });
      }
    } catch (error) {
      console.error('❌ File upload error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to upload file',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Get user Excel sessions
  async getSessions(req, res) {
    try {
      const userId = req.user.userId;
      const { 
        status = 'ready', 
        limit = 20, 
        skip = 0, 
        sortBy = 'lastAccessed', 
        sortOrder = 'desc' 
      } = req.query;

      const sessions = await ExcelSession.findUserSessions(userId, {
        status,
        limit: parseInt(limit),
        skip: parseInt(skip),
        sortBy,
        sortOrder: sortOrder === 'desc' ? -1 : 1
      });

      res.status(200).json({
        success: true,
        message: 'Excel sessions retrieved successfully',
        data: {
          sessions: sessions.map(session => ({
            sessionId: session.sessionId,
            fileName: session.originalFileName,
            fileSize: session.fileSize,
            status: session.status,
            sheetCount: session.workbook.sheetCount,
            lastAccessed: session.lastAccessed,
            createdAt: session.createdAt,
            conversationTitle: session.conversationId ? session.conversationId.title : null
          })),
          pagination: {
            total: sessions.length,
            limit: parseInt(limit),
            skip: parseInt(skip),
            hasMore: sessions.length === parseInt(limit)
          }
        }
      });
    } catch (error) {
      console.error('❌ Get sessions error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve sessions',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Delete Excel session
  async deleteSession(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.userId;

      const session = await ExcelSession.findOne({
        $or: [
          { _id: id, userId },
          { sessionId: id, userId }
        ]
      });

      if (!session) {
        return res.status(404).json({
          success: false,
          message: 'Session not found'
        });
      }

      // Delete the file from disk
      try {
        await fs.unlink(session.filePath);
      } catch (fileError) {
        console.warn('⚠️ Failed to delete file from disk:', fileError.message);
      }

      // Delete the session from database
      await ExcelSession.findByIdAndDelete(session._id);

      res.status(200).json({
        success: true,
        message: 'Session deleted successfully',
        data: {
          sessionId: session.sessionId,
          fileName: session.originalFileName
        }
      });
    } catch (error) {
      console.error('❌ Delete session error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to delete session',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Get session details
  async getSessionDetails(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.userId;

      const session = await ExcelSession.findOne({
        $or: [
          { _id: id, userId },
          { sessionId: id, userId }
        ]
      }).populate('conversationId', 'title description');

      if (!session) {
        return res.status(404).json({
          success: false,
          message: 'Session not found'
        });
      }

      // Update last accessed
      await session.updateLastAccessed();

      res.status(200).json({
        success: true,
        message: 'Session details retrieved successfully',
        data: {
          session: {
            sessionId: session.sessionId,
            fileName: session.originalFileName,
            fileSize: session.fileSize,
            mimeType: session.mimeType,
            status: session.status,
            workbook: session.workbook,
            dataAnalysis: session.dataAnalysis,
            formulaGenerations: session.formulaGenerations,
            optimizations: session.optimizations,
            processing: session.processing,
            settings: session.settings,
            lastAccessed: session.lastAccessed,
            createdAt: session.createdAt,
            updatedAt: session.updatedAt,
            conversation: session.conversationId
          }
        }
      });
    } catch (error) {
      console.error('❌ Get session details error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve session details',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }
}

module.exports = new ExcelController();
