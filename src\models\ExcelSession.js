const mongoose = require('mongoose');

const dataAnalysisSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['structure', 'statistics', 'patterns', 'quality', 'summary'],
    required: true
  },
  result: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  processingTime: {
    type: Number,
    default: 0
  }
}, {
  _id: true
});

const formulaGenerationSchema = new mongoose.Schema({
  requirement: {
    type: String,
    required: true,
    maxlength: [1000, 'Requirement cannot exceed 1000 characters']
  },
  generatedFormula: {
    type: String,
    required: true
  },
  explanation: {
    type: String,
    required: true
  },
  cellRange: {
    type: String,
    default: null
  },
  isApplied: {
    type: Boolean,
    default: false
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
}, {
  _id: true
});

const excelSessionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  conversationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Conversation',
    required: true,
    index: true
  },
  sessionId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  fileName: {
    type: String,
    required: true,
    trim: true
  },
  originalFileName: {
    type: String,
    required: true,
    trim: true
  },
  filePath: {
    type: String,
    required: true
  },
  fileSize: {
    type: Number,
    required: true,
    min: [0, 'File size cannot be negative']
  },
  mimeType: {
    type: String,
    required: true,
    enum: [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv' // .csv
    ]
  },
  status: {
    type: String,
    enum: ['uploading', 'processing', 'ready', 'error', 'archived'],
    default: 'uploading',
    index: true
  },
  workbook: {
    sheetCount: {
      type: Number,
      default: 0
    },
    sheets: [{
      name: {
        type: String,
        required: true
      },
      index: {
        type: Number,
        required: true
      },
      rowCount: {
        type: Number,
        default: 0
      },
      columnCount: {
        type: Number,
        default: 0
      },
      hasHeaders: {
        type: Boolean,
        default: false
      },
      dataTypes: [{
        column: String,
        type: {
          type: String,
          enum: ['string', 'number', 'date', 'boolean', 'formula', 'mixed']
        },
        samples: [String]
      }],
      statistics: {
        totalCells: {
          type: Number,
          default: 0
        },
        emptyCells: {
          type: Number,
          default: 0
        },
        formulaCells: {
          type: Number,
          default: 0
        }
      }
    }],
    metadata: {
      creator: String,
      lastModified: Date,
      application: String,
      version: String
    }
  },
  dataAnalysis: [dataAnalysisSchema],
  formulaGenerations: [formulaGenerationSchema],
  optimizations: {
    isOptimized: {
      type: Boolean,
      default: false
    },
    originalSize: {
      type: Number,
      default: 0
    },
    optimizedSize: {
      type: Number,
      default: 0
    },
    compressionRatio: {
      type: Number,
      default: 0
    },
    optimizationMethods: [{
      type: String,
      enum: ['sampling', 'compression', 'summarization', 'filtering']
    }],
    lastOptimized: {
      type: Date,
      default: null
    }
  },
  processing: {
    startTime: {
      type: Date,
      default: null
    },
    endTime: {
      type: Date,
      default: null
    },
    duration: {
      type: Number,
      default: 0
    },
    errors: [{
      message: String,
      stack: String,
      timestamp: {
        type: Date,
        default: Date.now
      }
    }],
    warnings: [{
      message: String,
      timestamp: {
        type: Date,
        default: Date.now
      }
    }]
  },
  settings: {
    autoAnalyze: {
      type: Boolean,
      default: true
    },
    maxRows: {
      type: Number,
      default: 10000
    },
    includeFormulas: {
      type: Boolean,
      default: true
    },
    detectDataTypes: {
      type: Boolean,
      default: true
    }
  },
  lastAccessed: {
    type: Date,
    default: Date.now
  },
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
excelSessionSchema.index({ userId: 1, status: 1 });
excelSessionSchema.index({ conversationId: 1 });
excelSessionSchema.index({ sessionId: 1 });
excelSessionSchema.index({ lastAccessed: 1 });
excelSessionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Pre-save middleware to calculate processing duration
excelSessionSchema.pre('save', function(next) {
  if (this.processing.startTime && this.processing.endTime) {
    this.processing.duration = this.processing.endTime - this.processing.startTime;
  }
  next();
});

// Instance method to start processing
excelSessionSchema.methods.startProcessing = function() {
  this.status = 'processing';
  this.processing.startTime = new Date();
  return this.save();
};

// Instance method to complete processing
excelSessionSchema.methods.completeProcessing = function(success = true) {
  this.status = success ? 'ready' : 'error';
  this.processing.endTime = new Date();
  return this.save();
};

// Instance method to add analysis
excelSessionSchema.methods.addAnalysis = function(analysisData) {
  this.dataAnalysis.push(analysisData);
  return this.save();
};

// Instance method to add formula generation
excelSessionSchema.methods.addFormulaGeneration = function(formulaData) {
  this.formulaGenerations.push(formulaData);
  return this.save();
};

// Instance method to update last accessed
excelSessionSchema.methods.updateLastAccessed = function() {
  this.lastAccessed = new Date();
  return this.save();
};

// Static method to find user sessions
excelSessionSchema.statics.findUserSessions = function(userId, options = {}) {
  const {
    status = 'ready',
    limit = 20,
    skip = 0,
    sortBy = 'lastAccessed',
    sortOrder = -1
  } = options;

  return this.find({ userId, status })
    .sort({ [sortBy]: sortOrder })
    .limit(limit)
    .skip(skip)
    .populate('conversationId', 'title');
};

// Static method to cleanup expired sessions
excelSessionSchema.statics.cleanupExpired = function() {
  return this.deleteMany({
    expiresAt: { $lt: new Date() }
  });
};

const ExcelSession = mongoose.model('ExcelSession', excelSessionSchema);

module.exports = ExcelSession;
