const User = require('../../../src/models/User');
const bcrypt = require('bcryptjs');

describe('User Model', () => {
  describe('User Creation', () => {
    test('should create a user with valid data', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'TestPassword123',
        firstName: 'John',
        lastName: 'Doe'
      };

      const user = new User(userData);
      const savedUser = await user.save();

      expect(savedUser._id).toBeDefined();
      expect(savedUser.email).toBe(userData.email.toLowerCase());
      expect(savedUser.firstName).toBe(userData.firstName);
      expect(savedUser.lastName).toBe(userData.lastName);
      expect(savedUser.role).toBe('user');
      expect(savedUser.isActive).toBe(true);
      expect(savedUser.isEmailVerified).toBe(false);
    });

    test('should hash password before saving', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'TestPassword123',
        firstName: 'John',
        lastName: 'Doe'
      };

      const user = new User(userData);
      await user.save();

      expect(user.password).not.toBe(userData.password);
      expect(user.password).toMatch(/^\$2[aby]\$\d+\$/); // bcrypt hash pattern
    });

    test('should not hash password if not modified', async () => {
      const user = await global.testUtils.createTestUser();
      const originalPassword = user.password;

      user.firstName = 'Updated Name';
      await user.save();

      expect(user.password).toBe(originalPassword);
    });

    test('should validate required fields', async () => {
      const user = new User({});

      let error;
      try {
        await user.save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.errors.email).toBeDefined();
      expect(error.errors.password).toBeDefined();
      expect(error.errors.firstName).toBeDefined();
      expect(error.errors.lastName).toBeDefined();
    });

    test('should validate email format', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'TestPassword123',
        firstName: 'John',
        lastName: 'Doe'
      };

      const user = new User(userData);

      let error;
      try {
        await user.save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.errors.email).toBeDefined();
    });

    test('should enforce unique email', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'TestPassword123',
        firstName: 'John',
        lastName: 'Doe'
      };

      await global.testUtils.createTestUser(userData);

      const duplicateUser = new User(userData);

      let error;
      try {
        await duplicateUser.save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.code).toBe(11000); // MongoDB duplicate key error
    });
  });

  describe('User Methods', () => {
    test('should compare password correctly', async () => {
      const password = 'TestPassword123';
      const user = await global.testUtils.createTestUser({ password });

      const isValid = await user.comparePassword(password);
      const isInvalid = await user.comparePassword('WrongPassword');

      expect(isValid).toBe(true);
      expect(isInvalid).toBe(false);
    });

    test('should generate auth token', async () => {
      const user = await global.testUtils.createTestUser();
      const token = user.generateAuthToken();

      expect(token).toBeDefined();
      expect(typeof token).toBe('string');

      // Verify token structure
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      expect(decoded.userId).toBe(user._id.toString());
      expect(decoded.email).toBe(user.email);
      expect(decoded.role).toBe(user.role);
    });

    test('should update last login', async () => {
      const user = await global.testUtils.createTestUser();
      const originalLastLogin = user.lastLogin;

      await user.updateLastLogin();

      expect(user.lastLogin).not.toBe(originalLastLogin);
      expect(user.lastLogin).toBeInstanceOf(Date);
      expect(user.usage.lastActivity).toBeInstanceOf(Date);
    });

    test('should increment usage stats', async () => {
      const user = await global.testUtils.createTestUser();
      const originalSessions = user.usage.totalSessions;
      const originalMessages = user.usage.totalMessages;
      const originalFiles = user.usage.totalFilesUploaded;

      await user.incrementUsage('session');
      expect(user.usage.totalSessions).toBe(originalSessions + 1);

      await user.incrementUsage('message');
      expect(user.usage.totalMessages).toBe(originalMessages + 1);

      await user.incrementUsage('file');
      expect(user.usage.totalFilesUploaded).toBe(originalFiles + 1);
    });
  });

  describe('User Statics', () => {
    test('should find user by email', async () => {
      const email = '<EMAIL>';
      const createdUser = await global.testUtils.createTestUser({ email });

      const foundUser = await User.findByEmail(email);
      const foundUserUpperCase = await User.findByEmail(email.toUpperCase());

      expect(foundUser).toBeDefined();
      expect(foundUser._id.toString()).toBe(createdUser._id.toString());
      expect(foundUserUpperCase).toBeDefined();
      expect(foundUserUpperCase._id.toString()).toBe(createdUser._id.toString());
    });

    test('should return null for non-existent email', async () => {
      const foundUser = await User.findByEmail('<EMAIL>');
      expect(foundUser).toBeNull();
    });
  });

  describe('User Virtuals', () => {
    test('should return full name', async () => {
      const user = await global.testUtils.createTestUser({
        firstName: 'John',
        lastName: 'Doe'
      });

      expect(user.fullName).toBe('John Doe');
    });
  });

  describe('User JSON Transform', () => {
    test('should exclude password from JSON', async () => {
      const user = await global.testUtils.createTestUser();
      const userJSON = user.toJSON();

      expect(userJSON.password).toBeUndefined();
      expect(userJSON.__v).toBeUndefined();
      expect(userJSON.email).toBeDefined();
      expect(userJSON.firstName).toBeDefined();
    });
  });

  describe('User Preferences', () => {
    test('should have default preferences', async () => {
      const user = await global.testUtils.createTestUser();

      expect(user.preferences.theme).toBe('auto');
      expect(user.preferences.language).toBe('en');
      expect(user.preferences.notifications.email).toBe(true);
      expect(user.preferences.notifications.push).toBe(true);
    });

    test('should allow custom preferences', async () => {
      const customPreferences = {
        theme: 'dark',
        language: 'es',
        notifications: {
          email: false,
          push: true
        }
      };

      const user = await global.testUtils.createTestUser({
        preferences: customPreferences
      });

      expect(user.preferences.theme).toBe('dark');
      expect(user.preferences.language).toBe('es');
      expect(user.preferences.notifications.email).toBe(false);
      expect(user.preferences.notifications.push).toBe(true);
    });
  });

  describe('User Subscription', () => {
    test('should have default subscription', async () => {
      const user = await global.testUtils.createTestUser();

      expect(user.subscription.plan).toBe('free');
      expect(user.subscription.isActive).toBe(false);
      expect(user.subscription.startDate).toBeNull();
      expect(user.subscription.endDate).toBeNull();
    });

    test('should allow custom subscription', async () => {
      const subscription = {
        plan: 'premium',
        isActive: true,
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      };

      const user = await global.testUtils.createTestUser({ subscription });

      expect(user.subscription.plan).toBe('premium');
      expect(user.subscription.isActive).toBe(true);
      expect(user.subscription.startDate).toBeInstanceOf(Date);
      expect(user.subscription.endDate).toBeInstanceOf(Date);
    });
  });
});
