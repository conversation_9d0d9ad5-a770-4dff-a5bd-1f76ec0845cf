const express = require('express');
const { body, param, query } = require('express-validator');
const multer = require('multer');
const path = require('path');
const excelController = require('../controllers/excelController');
const { authenticate, trackActivity, checkSubscription } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');
const config = require('../config/environment');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, config.upload.uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = config.upload.allowedFileTypes;
  const fileExt = path.extname(file.originalname).toLowerCase();
  
  if (allowedTypes.includes(fileExt)) {
    cb(null, true);
  } else {
    cb(new Error(`File type ${fileExt} is not allowed. Allowed types: ${allowedTypes.join(', ')}`), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: config.upload.maxFileSize
  },
  fileFilter: fileFilter
});

// Validation rules
const analyzeDataValidation = [
  body('data')
    .notEmpty()
    .withMessage('Data is required for analysis'),
  body('analysisType')
    .optional()
    .isIn(['structure', 'statistics', 'patterns', 'quality'])
    .withMessage('Invalid analysis type')
];

const generateFormulaValidation = [
  body('requirement')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Requirement must be between 1 and 1000 characters'),
  body('dataContext')
    .optional()
    .isObject()
    .withMessage('Data context must be an object')
];

const sessionsValidation = [
  query('status')
    .optional()
    .isIn(['uploading', 'processing', 'ready', 'error', 'archived'])
    .withMessage('Invalid status'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  query('skip')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Skip must be a non-negative integer'),
  query('sortBy')
    .optional()
    .isIn(['lastAccessed', 'createdAt', 'fileName', 'fileSize'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc')
];

const sessionIdValidation = [
  param('id')
    .notEmpty()
    .withMessage('Session ID is required')
];

// Routes

/**
 * @route   POST /api/excel/analyze
 * @desc    Analyze Excel data
 * @access  Private
 */
router.post('/analyze', 
  authenticate, 
  trackActivity, 
  checkSubscription('free'),
  analyzeDataValidation, 
  validateRequest, 
  excelController.analyzeData
);

/**
 * @route   POST /api/excel/formula
 * @desc    Generate Excel formula
 * @access  Private
 */
router.post('/formula', 
  authenticate, 
  trackActivity, 
  checkSubscription('free'),
  generateFormulaValidation, 
  validateRequest, 
  excelController.generateFormula
);

/**
 * @route   POST /api/excel/upload
 * @desc    Upload Excel file
 * @access  Private
 */
router.post('/upload', 
  authenticate, 
  trackActivity, 
  checkSubscription('free'),
  (req, res, next) => {
    upload.single('file')(req, res, (err) => {
      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            success: false,
            message: `File too large. Maximum size is ${config.upload.maxFileSize / 1024 / 1024}MB`
          });
        }
        return res.status(400).json({
          success: false,
          message: `Upload error: ${err.message}`
        });
      } else if (err) {
        return res.status(400).json({
          success: false,
          message: err.message
        });
      }
      next();
    });
  },
  excelController.uploadFile
);

/**
 * @route   GET /api/excel/sessions
 * @desc    Get user Excel sessions
 * @access  Private
 */
router.get('/sessions', 
  authenticate, 
  trackActivity,
  sessionsValidation, 
  validateRequest, 
  excelController.getSessions
);

/**
 * @route   GET /api/excel/sessions/:id
 * @desc    Get Excel session details
 * @access  Private
 */
router.get('/sessions/:id', 
  authenticate, 
  trackActivity,
  sessionIdValidation, 
  validateRequest, 
  excelController.getSessionDetails
);

/**
 * @route   DELETE /api/excel/session/:id
 * @desc    Delete Excel session
 * @access  Private
 */
router.delete('/session/:id', 
  authenticate, 
  trackActivity,
  sessionIdValidation, 
  validateRequest, 
  excelController.deleteSession
);

/**
 * @route   POST /api/excel/sessions/:id/analyze
 * @desc    Analyze specific Excel session
 * @access  Private
 */
router.post('/sessions/:id/analyze', 
  authenticate, 
  trackActivity, 
  checkSubscription('free'),
  sessionIdValidation,
  body('analysisType')
    .optional()
    .isIn(['structure', 'statistics', 'patterns', 'quality'])
    .withMessage('Invalid analysis type'),
  validateRequest,
  async (req, res) => {
    try {
      const { id } = req.params;
      const { analysisType = 'structure' } = req.body;
      const userId = req.user.userId;

      const ExcelSession = require('../models/ExcelSession');
      const session = await ExcelSession.findOne({
        $or: [
          { _id: id, userId },
          { sessionId: id, userId }
        ]
      });

      if (!session) {
        return res.status(404).json({
          success: false,
          message: 'Session not found'
        });
      }

      if (session.status !== 'ready') {
        return res.status(400).json({
          success: false,
          message: 'Session is not ready for analysis'
        });
      }

      // Use the workbook data for analysis
      const excelProcessor = require('../services/excelProcessor');
      const analysis = await excelProcessor.analyzeData(session.workbook, analysisType);

      // Save analysis to session
      await session.addAnalysis({
        type: analysisType,
        result: analysis.result,
        timestamp: new Date(),
        processingTime: 0
      });

      res.status(200).json({
        success: true,
        message: 'Session analysis completed successfully',
        data: {
          sessionId: session.sessionId,
          analysis,
          timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('❌ Session analysis error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to analyze session',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/excel/sessions/:id/optimize
 * @desc    Optimize Excel session data
 * @access  Private
 */
router.post('/sessions/:id/optimize', 
  authenticate, 
  trackActivity, 
  checkSubscription('basic'),
  sessionIdValidation,
  body('targetSize')
    .optional()
    .isInt({ min: 100, max: 10000 })
    .withMessage('Target size must be between 100 and 10000 tokens'),
  validateRequest,
  async (req, res) => {
    try {
      const { id } = req.params;
      const { targetSize = 2000 } = req.body;
      const userId = req.user.userId;

      const ExcelSession = require('../models/ExcelSession');
      const session = await ExcelSession.findOne({
        $or: [
          { _id: id, userId },
          { sessionId: id, userId }
        ]
      });

      if (!session) {
        return res.status(404).json({
          success: false,
          message: 'Session not found'
        });
      }

      if (session.status !== 'ready') {
        return res.status(400).json({
          success: false,
          message: 'Session is not ready for optimization'
        });
      }

      const dataOptimizer = require('../services/dataOptimizer');
      const optimization = await dataOptimizer.optimizeForAI(session.workbook, targetSize);

      // Update session optimization info
      session.optimizations = {
        isOptimized: true,
        originalSize: optimization.originalSize,
        optimizedSize: optimization.optimizedSize,
        compressionRatio: optimization.compressionRatio,
        optimizationMethods: [optimization.strategy],
        lastOptimized: new Date()
      };

      await session.save();

      res.status(200).json({
        success: true,
        message: 'Session optimization completed successfully',
        data: {
          sessionId: session.sessionId,
          optimization: {
            originalSize: optimization.originalSize,
            optimizedSize: optimization.optimizedSize,
            compressionRatio: optimization.compressionRatio,
            strategy: optimization.strategy,
            processingTime: optimization.processingTime
          }
        }
      });
    } catch (error) {
      console.error('❌ Session optimization error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to optimize session',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }
);

module.exports = router;
