{"name": "@smithy/util-base64", "version": "4.0.0", "description": "A Base64 <-> UInt8Array converter", "main": "./dist-cjs/index.js", "module": "./dist-es/index.js", "scripts": {"build": "concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types && yarn build:types:downlevel'", "build:cjs": "node ../../scripts/inline util-base64", "build:es": "yarn g:tsc -p tsconfig.es.json", "build:types": "yarn g:tsc -p tsconfig.types.json", "build:types:downlevel": "rimraf dist-types/ts3.4 && downlevel-dts dist-types dist-types/ts3.4", "stage-release": "rimraf ./.release && yarn pack && mkdir ./.release && tar zxvf ./package.tgz --directory ./.release && rm ./package.tgz", "clean": "rimraf ./dist-* && rimraf *.tsbuildinfo || exit 0", "lint": "eslint -c ../../.eslintrc.js \"src/**/*.ts\"", "format": "prettier --config ../../prettier.config.js --ignore-path ../.prettierignore --write \"**/*.{ts,md,json}\"", "test": "yarn g:vitest run", "test:watch": "yarn g:vitest watch"}, "author": {"name": "AWS SDK for JavaScript Team", "url": "https://aws.amazon.com/javascript/"}, "license": "Apache-2.0", "dependencies": {"@smithy/util-buffer-from": "^4.0.0", "@smithy/util-utf8": "^4.0.0", "tslib": "^2.6.2"}, "devDependencies": {"@types/node": "^18.11.9", "concurrently": "7.0.0", "downlevel-dts": "0.10.1", "rimraf": "3.0.2", "typedoc": "0.23.23"}, "types": "./dist-types/index.d.ts", "engines": {"node": ">=18.0.0"}, "typesVersions": {"<4.0": {"dist-types/*": ["dist-types/ts3.4/*"]}}, "files": ["dist-*/**"], "browser": {"./dist-es/fromBase64": "./dist-es/fromBase64.browser", "./dist-es/toBase64": "./dist-es/toBase64.browser"}, "react-native": {"./dist-es/fromBase64": "./dist-es/fromBase64.browser", "./dist-es/toBase64": "./dist-es/toBase64.browser", "./dist-cjs/fromBase64": "./dist-cjs/fromBase64.browser", "./dist-cjs/toBase64": "./dist-cjs/toBase64.browser"}, "homepage": "https://github.com/awslabs/smithy-typescript/tree/main/packages/util-base64", "repository": {"type": "git", "url": "https://github.com/awslabs/smithy-typescript.git", "directory": "packages/util-base64"}, "typedoc": {"entryPoint": "src/index.ts"}, "publishConfig": {"directory": ".release/package"}}