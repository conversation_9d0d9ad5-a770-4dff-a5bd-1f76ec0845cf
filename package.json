{"name": "excel-chat-backend", "version": "1.0.0", "description": "Node.js/Express.js API service that provides intelligent Excel assistance through OpenAI's platform", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "db:setup": "node scripts/setup.js", "db:migrate": "node scripts/migrate.js", "build": "echo 'Build process'", "deploy:production": "echo 'Deploy to production'", "logs:production": "echo 'Production logs'", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/"}, "keywords": ["excel", "ai", "openai", "assistant", "api", "nodejs", "express"], "author": "Excel Chat AI Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "openai": "^4.20.1", "prom-client": "^15.1.0", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.55.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "mongodb-memory-server": "^8.16.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}