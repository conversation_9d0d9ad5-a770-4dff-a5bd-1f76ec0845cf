// Test user fixtures
const users = {
  // Standard test user
  testUser: {
    email: '<EMAIL>',
    password: 'TestPassword123',
    firstName: 'Test',
    lastName: 'User',
    role: 'user',
    isActive: true,
    isEmailVerified: true,
    preferences: {
      theme: 'light',
      language: 'en',
      notifications: {
        email: true,
        push: true
      }
    },
    subscription: {
      plan: 'free',
      isActive: false
    }
  },

  // Admin user
  adminUser: {
    email: '<EMAIL>',
    password: 'AdminPassword123',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    isActive: true,
    isEmailVerified: true,
    preferences: {
      theme: 'dark',
      language: 'en',
      notifications: {
        email: true,
        push: false
      }
    },
    subscription: {
      plan: 'enterprise',
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
    }
  },

  // Premium user
  premiumUser: {
    email: '<EMAIL>',
    password: 'PremiumPassword123',
    firstName: 'Premium',
    lastName: 'User',
    role: 'user',
    isActive: true,
    isEmailVerified: true,
    preferences: {
      theme: 'auto',
      language: 'en',
      notifications: {
        email: true,
        push: true
      }
    },
    subscription: {
      plan: 'premium',
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
    },
    usage: {
      totalSessions: 50,
      totalMessages: 200,
      totalFilesUploaded: 10
    }
  },

  // Inactive user
  inactiveUser: {
    email: '<EMAIL>',
    password: 'InactivePassword123',
    firstName: 'Inactive',
    lastName: 'User',
    role: 'user',
    isActive: false,
    isEmailVerified: false,
    subscription: {
      plan: 'free',
      isActive: false
    }
  },

  // User with expired subscription
  expiredUser: {
    email: '<EMAIL>',
    password: 'ExpiredPassword123',
    firstName: 'Expired',
    lastName: 'User',
    role: 'user',
    isActive: true,
    isEmailVerified: true,
    subscription: {
      plan: 'basic',
      isActive: false,
      startDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 60 days ago
      endDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
    }
  },

  // User with high usage
  heavyUser: {
    email: '<EMAIL>',
    password: 'HeavyPassword123',
    firstName: 'Heavy',
    lastName: 'User',
    role: 'user',
    isActive: true,
    isEmailVerified: true,
    subscription: {
      plan: 'premium',
      isActive: true,
      startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    },
    usage: {
      totalSessions: 500,
      totalMessages: 2000,
      totalFilesUploaded: 100,
      lastActivity: new Date()
    }
  },

  // User with custom preferences
  customUser: {
    email: '<EMAIL>',
    password: 'CustomPassword123',
    firstName: 'Custom',
    lastName: 'User',
    role: 'user',
    isActive: true,
    isEmailVerified: true,
    preferences: {
      theme: 'dark',
      language: 'es',
      notifications: {
        email: false,
        push: true
      }
    },
    subscription: {
      plan: 'basic',
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    }
  }
};

// Helper function to create multiple users
const createUsers = async (userKeys = []) => {
  const User = require('../../src/models/User');
  const createdUsers = {};

  for (const key of userKeys) {
    if (users[key]) {
      const user = new User(users[key]);
      createdUsers[key] = await user.save();
    }
  }

  return createdUsers;
};

// Helper function to create a user with custom data
const createCustomUser = async (customData = {}) => {
  const User = require('../../src/models/User');
  const userData = {
    ...users.testUser,
    ...customData
  };
  
  const user = new User(userData);
  return await user.save();
};

// Helper function to get user data without creating
const getUserData = (userKey) => {
  return users[userKey] ? { ...users[userKey] } : null;
};

// Helper function to get all user keys
const getUserKeys = () => {
  return Object.keys(users);
};

module.exports = {
  users,
  createUsers,
  createCustomUser,
  getUserData,
  getUserKeys
};
