const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  role: {
    type: String,
    enum: ['user', 'assistant', 'system'],
    required: true
  },
  content: {
    type: String,
    required: true,
    maxlength: [10000, 'Message content cannot exceed 10000 characters']
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  metadata: {
    tokens: {
      type: Number,
      default: 0
    },
    model: {
      type: String,
      default: null
    },
    processingTime: {
      type: Number,
      default: 0
    },
    excelData: {
      type: mongoose.Schema.Types.Mixed,
      default: null
    },
    functionCalls: [{
      name: String,
      parameters: mongoose.Schema.Types.Mixed,
      result: mongoose.Schema.Types.Mixed,
      timestamp: {
        type: Date,
        default: Date.now
      }
    }]
  }
}, {
  _id: true
});

const conversationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  threadId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  status: {
    type: String,
    enum: ['active', 'archived', 'deleted'],
    default: 'active',
    index: true
  },
  messages: [messageSchema],
  excelContext: {
    hasExcelData: {
      type: Boolean,
      default: false
    },
    fileName: {
      type: String,
      default: null
    },
    fileSize: {
      type: Number,
      default: 0
    },
    sheetNames: [{
      type: String
    }],
    dataStructure: {
      type: mongoose.Schema.Types.Mixed,
      default: null
    },
    lastUpdated: {
      type: Date,
      default: null
    }
  },
  analytics: {
    totalMessages: {
      type: Number,
      default: 0
    },
    totalTokens: {
      type: Number,
      default: 0
    },
    averageResponseTime: {
      type: Number,
      default: 0
    },
    functionCallsCount: {
      type: Number,
      default: 0
    },
    lastActivity: {
      type: Date,
      default: Date.now
    }
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  isStarred: {
    type: Boolean,
    default: false
  },
  sharedWith: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    permission: {
      type: String,
      enum: ['read', 'write'],
      default: 'read'
    },
    sharedAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
conversationSchema.index({ userId: 1, status: 1 });
conversationSchema.index({ threadId: 1 });
conversationSchema.index({ 'analytics.lastActivity': -1 });
conversationSchema.index({ tags: 1 });
conversationSchema.index({ isStarred: 1 });

// Pre-save middleware to update analytics
conversationSchema.pre('save', function(next) {
  if (this.isModified('messages')) {
    this.analytics.totalMessages = this.messages.length;
    this.analytics.totalTokens = this.messages.reduce((total, msg) => total + (msg.metadata.tokens || 0), 0);
    this.analytics.functionCallsCount = this.messages.reduce((total, msg) => total + (msg.metadata.functionCalls?.length || 0), 0);
    this.analytics.lastActivity = new Date();
    
    // Calculate average response time
    const responseTimes = this.messages
      .filter(msg => msg.role === 'assistant' && msg.metadata.processingTime > 0)
      .map(msg => msg.metadata.processingTime);
    
    if (responseTimes.length > 0) {
      this.analytics.averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    }
  }
  next();
});

// Instance method to add message
conversationSchema.methods.addMessage = function(messageData) {
  this.messages.push(messageData);
  return this.save();
};

// Instance method to get recent messages
conversationSchema.methods.getRecentMessages = function(limit = 10) {
  return this.messages.slice(-limit);
};

// Instance method to update Excel context
conversationSchema.methods.updateExcelContext = function(excelData) {
  this.excelContext = {
    ...this.excelContext,
    ...excelData,
    lastUpdated: new Date()
  };
  return this.save();
};

// Static method to find user conversations
conversationSchema.statics.findUserConversations = function(userId, options = {}) {
  const {
    status = 'active',
    limit = 20,
    skip = 0,
    sortBy = 'updatedAt',
    sortOrder = -1
  } = options;

  return this.find({ userId, status })
    .sort({ [sortBy]: sortOrder })
    .limit(limit)
    .skip(skip)
    .select('-messages'); // Exclude messages for list view
};

// Static method to search conversations
conversationSchema.statics.searchConversations = function(userId, searchTerm, options = {}) {
  const { limit = 10, skip = 0 } = options;
  
  return this.find({
    userId,
    status: 'active',
    $or: [
      { title: { $regex: searchTerm, $options: 'i' } },
      { description: { $regex: searchTerm, $options: 'i' } },
      { tags: { $in: [new RegExp(searchTerm, 'i')] } }
    ]
  })
  .sort({ updatedAt: -1 })
  .limit(limit)
  .skip(skip);
};

const Conversation = mongoose.model('Conversation', conversationSchema);

module.exports = Conversation;
