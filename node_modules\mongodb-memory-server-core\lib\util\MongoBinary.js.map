{"version": 3, "file": "MongoBinary.js", "sourceRoot": "", "sources": ["../../src/util/MongoBinary.ts"], "names": [], "mappings": ";;;;AAAA,yDAAoB;AACpB,6DAAwB;AACxB,6FAAwD;AACxD,8EAAmF;AACnF,+DAA0B;AAC1B,4DAAiC;AACjC,mCAA8D;AAC9D,iDAA0C;AAC1C,yCAAsC;AACtC,qDAA6E;AAE7E,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,qBAAqB,CAAC,CAAC;AAMzC;;GAEG;AACH,MAAa,WAAW;IACtB;;;;OAIG;IACH,MAAM,CAAO,QAAQ,CAAC,OAAkC;;YACtD,GAAG,CAAC,UAAU,CAAC,CAAC;YAChB,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;YACzC,qBAAqB;YACrB,MAAM,IAAA,aAAK,EAAC,WAAW,CAAC,CAAC;YAEzB,oBAAoB;YACpB,MAAM,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,OAAO,OAAO,CAAC,CAAC;YAC9D,GAAG,CAAC,wDAAwD,QAAQ,GAAG,CAAC,CAAC;YACzE,qBAAqB;YACrB,sDAAsD;YACtD,oDAAoD;YACpD,MAAM,IAAI,GAAG,MAAM,mBAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAExC,uEAAuE;YACvE,IAAI;gBACF,mDAAmD;gBACnD,IAAI,CAAC,+BAAc,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBAC5C,GAAG,CAAC,4BAA4B,OAAO,WAAW,CAAC,CAAC;oBACpD,MAAM,UAAU,GAAG,IAAI,6BAAmB,CAAC,OAAO,CAAC,CAAC;oBACpD,+BAAc,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC;iBAC3E;aACF;oBAAS;gBACR,GAAG,CAAC,kCAAkC,CAAC,CAAC;gBACxC,cAAc;gBACd,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;gBACpB,GAAG,CAAC,iCAAiC,CAAC,CAAC;aACxC;YAED,MAAM,SAAS,GAAG,+BAAc,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC1D,gEAAgE;YAChE,IAAA,iBAAS,EACP,OAAO,SAAS,KAAK,QAAQ,EAC7B,IAAI,KAAK,CAAC,8BAA8B,OAAO,yCAAyC,CAAC,CAC1F,CAAC;YAEF,OAAO,SAAS,CAAC;QACnB,CAAC;KAAA;IAED;;;;;OAKG;IACH,MAAM,CAAO,OAAO,CAAC,OAAwB,EAAE;;YAC7C,GAAG,CAAC,SAAS,CAAC,CAAC;YAEf,oGAAoG;YACpG,MAAM,OAAO,mCACR,CAAC,MAAM,+BAAc,CAAC,eAAe,CAAC,IAAiC,CAAC,CAAC,KAC5E,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAA,uBAAa,EAAC,sCAAsB,CAAC,QAAQ,CAAC,IAAI,YAAE,CAAC,QAAQ,EAAE,EAC1F,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,SAAS,CAAC,CAAC,GACtF,CAAC;YAEF,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAEvE,IAAI,UAAU,GAAuB,MAAM,+BAAc,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAEhF,+DAA+D;YAC/D,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE;gBAC1B,4JAA4J;gBAC5J,IAAI,CAAC,IAAA,yBAAiB,EAAC,UAAU,CAAC,EAAE;oBAClC,GAAG,CAAC,iCAAiC,UAAU,kBAAkB,CAAC,CAAC;oBACnE,MAAM,WAAW,GAAG,IAAA,yBAAS,EAAC,UAAU,EAAE,CAAC,WAAW,CAAC,CAAC;yBACrD,MAAM,CAAC,QAAQ,EAAE;wBAClB,kIAAkI;yBACjI,KAAK,CAAC,oEAAoE,CAAC,CAAC;oBAE/E,IAAA,iBAAS,EACP,CAAC,IAAA,yBAAiB,EAAC,WAAW,CAAC,EAC/B,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAChE,CAAC;oBAEF,8IAA8I;oBAC9I,IAAI,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,2BAA2B,CAAC,CAAC,EAAE;wBAChF,GAAG,CAAC,qDAAqD,CAAC,CAAC;wBAC3D,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;wBAErC,IAAI,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,aAAa,CAAC,EAAE;4BAC9C,mHAAmH;4BACnH,OAAO,CAAC,IAAI,CACV,yDAAyD;gCACvD,4BAA4B,aAAa,KAAK;gCAC9C,4BAA4B,OAAO,CAAC,OAAO,OAAO;gCAClD,uBAAuB,CAC1B,CAAC;yBACH;qBACF;iBACF;qBAAM;oBACL,MAAM,IAAI,KAAK,CACb,8JAA8J,CAC/J,CAAC;iBACH;aACF;YAED,IAAA,iBAAS,EACP,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EACnC,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAC7D,CAAC;YAEF,IAAI,CAAC,UAAU,EAAE;gBACf,IAAI,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,gBAAgB,CAAC,CAAC,EAAE;oBACrE,GAAG,CAAC,2DAA2D,CAAC,CAAC;oBACjE,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;iBAC3C;qBAAM;oBACL,GAAG,CAAC,yDAAyD,CAAC,CAAC;iBAChE;aACF;YAED,IAAI,CAAC,UAAU,EAAE;gBACf,MAAM,eAAe,GAAG,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBAC1F,MAAM,IAAI,KAAK,CACb,oEAAoE,UAAU,yBAAyB,eAAe,IAAI,CAC3H,CAAC;aACH;YAED,GAAG,CAAC,iCAAiC,UAAU,GAAG,CAAC,CAAC;YAEpD,OAAO,UAAU,CAAC;QACpB,CAAC;KAAA;CACF;AAhID,kCAgIC;AAED,kBAAe,WAAW,CAAC"}