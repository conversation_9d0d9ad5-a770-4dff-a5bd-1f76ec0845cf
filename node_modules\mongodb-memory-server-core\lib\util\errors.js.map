{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/util/errors.ts"], "names": [], "mappings": ";;;AAAA,mCAA4C;AAE5C,MAAa,UAAW,SAAQ,KAAK;IACnC,YAAmB,YAAsB,EAAS,QAAgB;QAChE,KAAK,CACH,mCAAmC,QAAQ,wBAAwB,YAAY,CAAC,IAAI,CAClF,GAAG,CACJ,MAAM;YACL,kHAAkH;YAClH,2JAA2J,CAC9J,CAAC;QAPe,iBAAY,GAAZ,YAAY,CAAU;QAAS,aAAQ,GAAR,QAAQ,CAAQ;IAQlE,CAAC;CACF;AAVD,gCAUC;AAED,MAAa,0BAA2B,SAAQ,KAAK;IACnD,YAAmB,MAAc;QAC/B,KAAK,CAAC,6BAA6B,MAAM,GAAG,CAAC,CAAC;QAD7B,WAAM,GAAN,MAAM,CAAQ;IAEjC,CAAC;CACF;AAJD,gEAIC;AAED,MAAa,2BAA4B,SAAQ,KAAK;IACpD,YAAmB,YAAqB,EAAS,IAAY;QAC3D,KAAK,CACH,uBAAuB,IAAI,uCACzB,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAC9B,EAAE,CACH,CAAC;QALe,iBAAY,GAAZ,YAAY,CAAS;QAAS,SAAI,GAAJ,IAAI,CAAQ;IAM7D,CAAC;CACF;AARD,kEAQC;AAED,MAAa,oBAAqB,SAAQ,KAAK;IAC7C,YAAmB,QAAgB;QACjC,KAAK,CAAC,sBAAsB,QAAQ,GAAG,CAAC,CAAC;QADxB,aAAQ,GAAR,QAAQ,CAAQ;IAEnC,CAAC;CACF;AAJD,oDAIC;AAED,MAAa,wBAAyB,SAAQ,KAAK;IACjD,YAAmB,IAAY,EAAS,QAAiB;QACvD,KAAK,EAAE,CAAC;QADS,SAAI,GAAJ,IAAI,CAAQ;QAAS,aAAQ,GAAR,QAAQ,CAAS;QAGvD,IAAI,CAAC,IAAA,yBAAiB,EAAC,QAAQ,CAAC,EAAE;YAChC,IAAI,CAAC,OAAO,GAAG,yDAAyD,IAAI,iBAAiB,QAAQ,GAAG,CAAC;SAC1G;aAAM;YACL,IAAI,CAAC,OAAO,GAAG,8BAA8B,IAAI,GAAG,CAAC;SACtD;IACH,CAAC;CACF;AAVD,4DAUC;AAED,MAAa,0BAA2B,SAAQ,KAAK;IACnD,YAAmB,OAAe,EAAS,KAAc;QACvD,KAAK,CAAC,mBAAmB,OAAO,2CAA2C,KAAK,IAAI,CAAC,CAAC;QADrE,YAAO,GAAP,OAAO,CAAQ;QAAS,UAAK,GAAL,KAAK,CAAS;IAEzD,CAAC;CACF;AAJD,gEAIC;AAED,yDAAyD;AACzD,MAAa,mBAAoB,SAAQ,KAAK;IAC5C,YAAmB,SAAkB;QACnC,KAAK,EAAE,CAAC;QADS,cAAS,GAAT,SAAS,CAAS;QAEnC,MAAM,WAAW,GAAG,kCAAkC,CAAC;QAEvD,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,OAAO,GAAG,GAAG,WAAW,wDAAwD,CAAC;SACvF;aAAM;YACL,IAAI,CAAC,OAAO,GAAG,GAAG,WAAW,qDAAqD,CAAC;SACpF;IACH,CAAC;CACF;AAXD,kDAWC;AAED,sDAAsD;AACtD,MAAa,wBAAyB,SAAQ,KAAK;IACjD,YAAmB,UAAkB;QACnC,KAAK,CACH,0DAA0D,UAAU,kCAAkC,CACvG,CAAC;QAHe,eAAU,GAAV,UAAU,CAAQ;IAIrC,CAAC;CACF;AAND,4DAMC;AAED,MAAa,mBAAoB,SAAQ,KAAK;IAC5C,YAAmB,SAAiB,EAAS,YAAoB;QAC/D,KAAK,CAAC,oCAAoC,SAAS,wBAAwB,YAAY,GAAG,CAAC,CAAC;QAD3E,cAAS,GAAT,SAAS,CAAQ;QAAS,iBAAY,GAAZ,YAAY,CAAQ;IAEjE,CAAC;CACF;AAJD,kDAIC;AAED,MAAa,sBAAuB,SAAQ,KAAK;IAC/C,YAAmB,MAAc;QAC/B,KAAK,CAAC,2DAA2D,MAAM,GAAG,CAAC,CAAC;QAD3D,WAAM,GAAN,MAAM,CAAQ;IAEjC,CAAC;CACF;AAJD,wDAIC;AAED,MAAa,iBAAkB,SAAQ,KAAK;IAC1C,YAAmB,KAAa;QAC9B,KAAK,CAAC,sEAAsE,KAAK,IAAI,CAAC,CAAC;QADtE,UAAK,GAAL,KAAK,CAAQ;IAEhC,CAAC;CACF;AAJD,8CAIC;AAED,MAAa,mBAAoB,SAAQ,KAAK;IAC5C;QACE,KAAK,CAAC,gDAAgD,CAAC,CAAC;IAC1D,CAAC;CACF;AAJD,kDAIC;AAED,MAAa,kBAAmB,SAAQ,KAAK;IAC3C;QACE,KAAK,CAAC,+CAA+C,CAAC,CAAC;IACzD,CAAC;CACF;AAJD,gDAIC;AAED,MAAa,4BAA6B,SAAQ,KAAK;IACrD,YAAmB,IAAY;QAC7B,KAAK,CAAC,SAAS,IAAI,uEAAuE,CAAC,CAAC;QAD3E,SAAI,GAAJ,IAAI,CAAQ;IAE/B,CAAC;CACF;AAJD,oEAIC;AAED,MAAa,mBAAoB,SAAQ,KAAK;IAC5C,YAAmB,IAAY;QAC7B,KAAK,CAAC,sBAAsB,IAAI,uBAAuB,CAAC,CAAC;QADxC,SAAI,GAAJ,IAAI,CAAQ;IAE/B,CAAC;CACF;AAJD,kDAIC;AAED;;GAEG;AACH,MAAa,sBAAuB,SAAQ,KAAK;IAC/C;QACE,KAAK,CAAC,iCAAiC,CAAC,CAAC;IAC3C,CAAC;CACF;AAJD,wDAIC;AAED,MAAa,oBAAqB,SAAQ,KAAK;IAC7C,YAAmB,KAAa;QAC9B,KAAK,CAAC,6DAA6D,KAAK,IAAI,CAAC,CAAC;QAD7D,UAAK,GAAL,KAAK,CAAQ;IAEhC,CAAC;CACF;AAJD,oDAIC;AAED,MAAa,sBAAuB,SAAQ,KAAK;IAC/C,YAAmB,GAAW;QAC5B,KAAK,CAAC,aAAa,GAAG,+BAA+B,CAAC,CAAC;QADtC,QAAG,GAAH,GAAG,CAAQ;IAE9B,CAAC;CACF;AAJD,wDAIC;AAED,MAAa,iBAAkB,SAAQ,KAAK;IAC1C,YAAmB,IAAY,EAAS,KAAc;QACpD,KAAK,EAAE,CAAC;QADS,SAAI,GAAJ,IAAI,CAAQ;QAAS,UAAK,GAAL,KAAK,CAAS;QAEpD,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,aAAa,IAAI,0BAA0B,QAAQ,EAAE,CAAC;IACvE,CAAC;CACF;AAND,8CAMC;AAED,MAAa,gCAAiC,SAAQ,KAAK;IACzD,YACS,IAAY,EACZ,iBAAyB,EACzB,kBAA0B,EAC1B,KAAc;QAErB,KAAK,EAAE,CAAC;QALD,SAAI,GAAJ,IAAI,CAAQ;QACZ,sBAAiB,GAAjB,iBAAiB,CAAQ;QACzB,uBAAkB,GAAlB,kBAAkB,CAAQ;QAC1B,UAAK,GAAL,KAAK,CAAS;QAIrB,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,sBAAsB,iBAAiB,2BAA2B,IAAI,2BAA2B,kBAAkB,IAAI,QAAQ,EAAE,CAAC;IACnJ,CAAC;CACF;AAZD,4EAYC;AAED;;GAEG;AACH,MAAa,mBAAoB,SAAQ,KAAK;IAC5C,+EAA+E;IAC/E,YAAY,GAAW;QACrB,KAAK,CAAC,GAAG,CAAC,CAAC;IACb,CAAC;CACF;AALD,kDAKC;AAED;;GAEG;AACH,MAAa,oBAAqB,SAAQ,KAAK;IAC7C,YAAY,IAAmB,EAAE,MAAqB;QACpD,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,OAAO,GAAG,2CAA2C,IAAI,iBAAiB,MAAM,GAAG,CAAC;QAEzF,IAAI,MAAM,IAAI,QAAQ,EAAE;YACtB,IAAI,CAAC,OAAO;gBACV,qJAAqJ,CAAC;SACzJ;QAED,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,CAAC,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,CAAC,CAAC,GAAG,UAAU,EAAE;YAC5D,IAAI,CAAC,OAAO;gBACV,qMAAqM,CAAC;SACzM;IACH,CAAC;CACF;AAhBD,oDAgBC;AAED;;GAEG;AACH,MAAa,mBAAoB,SAAQ,KAAK;IAC5C,YAAmB,OAAe;QAChC,KAAK,CAAC,2DAA2D,OAAO,IAAI,CAAC,CAAC;QAD7D,YAAO,GAAP,OAAO,CAAQ;IAElC,CAAC;CACF;AAJD,kDAIC;AAED;;GAEG;AACH,MAAa,aAAc,SAAQ,KAAK;IACtC,YAAmB,GAAW,EAAS,GAAW;QAChD,KAAK,CAAC,6BAA6B,GAAG,iBAAiB,GAAG,EAAE,CAAC,CAAC;QAD7C,QAAG,GAAH,GAAG,CAAQ;QAAS,QAAG,GAAH,GAAG,CAAQ;IAElD,CAAC;CACF;AAJD,sCAIC;AAED,wCAAwC;AACxC,MAAa,eAAgB,SAAQ,KAAK;CAAG;AAA7C,0CAA6C"}