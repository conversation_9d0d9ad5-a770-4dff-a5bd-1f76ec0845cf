const winston = require('winston');
const path = require('path');
const config = require('../config/environment');

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Create logs directory if it doesn't exist
const fs = require('fs');
const logsDir = path.dirname(config.logging.file);
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create logger instance
const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  defaultMeta: {
    service: 'excel-chat-backend',
    environment: config.server.nodeEnv
  },
  transports: [
    // Error log file
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      )
    }),

    // Combined log file
    new winston.transports.File({
      filename: config.logging.file,
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),

    // Separate file for API requests
    new winston.transports.File({
      filename: path.join(logsDir, 'api.log'),
      level: 'info',
      maxsize: 5242880, // 5MB
      maxFiles: 3,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          // Only log API-related messages
          if (meta.method && meta.url) {
            return JSON.stringify({ timestamp, level, message, ...meta });
          }
          return null;
        }),
        winston.format.filter(info => info.message !== null)
      )
    })
  ],

  // Handle exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log'),
      maxsize: 5242880,
      maxFiles: 3
    })
  ],

  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log'),
      maxsize: 5242880,
      maxFiles: 3
    })
  ]
});

// Add console transport for development
if (config.server.nodeEnv === 'development') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'debug'
  }));
}

// Add console transport for production (errors only)
if (config.server.nodeEnv === 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    ),
    level: 'error'
  }));
}

// Request logging middleware
const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  // Log request
  logger.info('Incoming request', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.userId,
    timestamp: new Date().toISOString()
  });

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    const duration = Date.now() - start;
    
    logger.info('Request completed', {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userId: req.user?.userId,
      timestamp: new Date().toISOString()
    });

    originalEnd.call(this, chunk, encoding);
  };

  next();
};

// Performance logging
const performanceLogger = {
  start: (operation) => {
    return {
      operation,
      startTime: Date.now(),
      end: function(metadata = {}) {
        const duration = Date.now() - this.startTime;
        logger.info('Performance metric', {
          operation: this.operation,
          duration: `${duration}ms`,
          ...metadata,
          timestamp: new Date().toISOString()
        });
        return duration;
      }
    };
  }
};

// Security logging
const securityLogger = {
  authFailure: (req, reason) => {
    logger.warn('Authentication failure', {
      reason,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  },

  suspiciousActivity: (req, activity, details = {}) => {
    logger.warn('Suspicious activity detected', {
      activity,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId,
      url: req.originalUrl,
      method: req.method,
      details,
      timestamp: new Date().toISOString()
    });
  },

  rateLimitExceeded: (req, limit) => {
    logger.warn('Rate limit exceeded', {
      limit,
      ip: req.ip,
      userId: req.user?.userId,
      url: req.originalUrl,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  }
};

// Business logic logging
const businessLogger = {
  userAction: (userId, action, details = {}) => {
    logger.info('User action', {
      userId,
      action,
      details,
      timestamp: new Date().toISOString()
    });
  },

  systemEvent: (event, details = {}) => {
    logger.info('System event', {
      event,
      details,
      timestamp: new Date().toISOString()
    });
  },

  externalService: (service, action, duration, success, details = {}) => {
    logger.info('External service call', {
      service,
      action,
      duration: `${duration}ms`,
      success,
      details,
      timestamp: new Date().toISOString()
    });
  }
};

// Error logging helper
const logError = (error, context = {}) => {
  logger.error('Application error', {
    message: error.message,
    stack: error.stack,
    name: error.name,
    ...context,
    timestamp: new Date().toISOString()
  });
};

// Health check logging
const healthLogger = {
  check: (service, status, details = {}) => {
    const level = status === 'healthy' ? 'info' : 'warn';
    logger.log(level, 'Health check', {
      service,
      status,
      details,
      timestamp: new Date().toISOString()
    });
  }
};

module.exports = {
  logger,
  requestLogger,
  performanceLogger,
  securityLogger,
  businessLogger,
  healthLogger,
  logError
};
