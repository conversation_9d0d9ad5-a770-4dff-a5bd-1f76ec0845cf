const { OpenAI } = require('openai');
const config = require('./environment');

class OpenAIConfig {
  constructor() {
    this.client = null;
    this.isInitialized = false;
  }

  initialize() {
    try {
      if (this.isInitialized) {
        return this.client;
      }

      if (!config.openai.apiKey) {
        throw new Error('OpenAI API key is required');
      }

      this.client = new OpenAI({
        apiKey: config.openai.apiKey,
      });

      this.isInitialized = true;
      console.log('🤖 OpenAI client initialized successfully');
      
      return this.client;
    } catch (error) {
      console.error('❌ Failed to initialize OpenAI client:', error.message);
      throw error;
    }
  }

  getClient() {
    if (!this.isInitialized) {
      return this.initialize();
    }
    return this.client;
  }

  getConfig() {
    return {
      model: config.openai.model,
      maxTokens: config.openai.maxTokens,
      temperature: config.openai.temperature,
      assistantId: config.openai.assistantId
    };
  }

  async healthCheck() {
    try {
      if (!this.isInitialized) {
        return { status: 'not_initialized', message: 'OpenAI client not initialized' };
      }

      // Simple API call to check connectivity
      const response = await this.client.models.list();
      
      return {
        status: 'healthy',
        message: 'OpenAI API connection is healthy',
        modelsCount: response.data.length
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        message: 'OpenAI API health check failed',
        error: error.message
      };
    }
  }

  // Assistant-specific configuration
  getAssistantConfig() {
    return {
      name: "Excel Chat AI Assistant",
      instructions: `You are an expert Excel assistant that helps users with:
        - Excel formulas and functions
        - Data analysis and visualization
        - Spreadsheet optimization
        - VBA and automation
        - Data cleaning and transformation
        - Chart creation and formatting
        
        Always provide clear, actionable advice with specific Excel formulas when applicable.
        If you need to see the user's data to provide better help, ask for it.
        Be concise but thorough in your explanations.`,
      model: config.openai.model,
      tools: [
        {
          type: "function",
          function: {
            name: "analyze_excel_data",
            description: "Analyze Excel data structure and provide insights",
            parameters: {
              type: "object",
              properties: {
                data: {
                  type: "object",
                  description: "Excel data to analyze"
                },
                analysis_type: {
                  type: "string",
                  enum: ["structure", "statistics", "patterns", "quality"],
                  description: "Type of analysis to perform"
                }
              },
              required: ["data", "analysis_type"]
            }
          }
        },
        {
          type: "function",
          function: {
            name: "generate_excel_formula",
            description: "Generate Excel formulas based on requirements",
            parameters: {
              type: "object",
              properties: {
                requirement: {
                  type: "string",
                  description: "Description of what the formula should do"
                },
                data_context: {
                  type: "object",
                  description: "Context about the data structure"
                }
              },
              required: ["requirement"]
            }
          }
        }
      ]
    };
  }
}

// Create singleton instance
const openaiConfig = new OpenAIConfig();

module.exports = openaiConfig;
