{"version": 3, "file": "MongoMemoryServer.d.ts", "sourceRoot": "", "sources": ["../src/MongoMemoryServer.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AAE7C,OAAO,EAOL,eAAe,EACf,OAAO,EAGR,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,uBAAuB,EAAE,MAAM,sBAAsB,CAAC;AAC1F,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAQtC;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACpC,QAAQ,CAAC,EAAE,uBAAuB,CAAC;IACnC,MAAM,CAAC,EAAE,eAAe,CAAC;IACzB,KAAK,CAAC,EAAE,YAAY,CAAC;IACrB;;OAEG;IACH,IAAI,CAAC,EAAE,aAAa,CAAC;CACtB;AAED,MAAM,WAAW,aAAa;IAC5B;;;OAGG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;OAGG;IACH,UAAU,CAAC,EAAE,UAAU,EAAE,CAAC;IAC1B;;;OAGG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB;;;OAGG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB;;;OAGG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB;;;;OAIG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;CACzB;AAGD;;GAEG;AACH,MAAM,WAAW,mBAAmB;IAClC,IAAI,EAAE,WAAW,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,CAAC;IACnD,MAAM,CAAC,EAAE,uBAAuB,CAAC,QAAQ,CAAC,CAAC;IAC3C,MAAM,EAAE,WAAW,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC;IACvD,EAAE,EAAE,WAAW,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/C,aAAa,EAAE,WAAW,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC,CAAC;IACrE,OAAO,CAAC,EAAE,WAAW,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC;IAC1D,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,eAAe,CAAC,EAAE,WAAW,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC,CAAC;IAC1E,aAAa,CAAC,EAAE,WAAW,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC,CAAC;CACvE;AAED;;GAEG;AACH,MAAM,WAAW,iBAAkB,SAAQ,mBAAmB;IAC5D,MAAM,EAAE,WAAW,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;IACnD,QAAQ,EAAE,aAAa,CAAC;CACzB;AAED;;GAEG;AACH,oBAAY,uBAAuB;IACjC,WAAW,gBAAgB;CAC5B;AAED;;GAEG;AACH,oBAAY,uBAAuB;IACjC,GAAG,QAAQ;IACX,QAAQ,aAAa;IACrB,OAAO,YAAY;IACnB,OAAO,YAAY;CACpB;AAED;;;GAGG;AACH,oBAAY,SAAS,GACjB,MAAM,GACN,WAAW,GACX,SAAS,GACT,SAAS,GACT,WAAW,GACX,cAAc,GACd,gBAAgB,GAChB,gBAAgB,GAChB,aAAa,GACb,QAAQ,GACR,SAAS,GACT,iBAAiB,GACjB,sBAAsB,GACtB,sBAAsB,GACtB,oBAAoB,GACpB,MAAM,GACN,MAAM,CAAC;AAEX;;;;;GAKG;AACH,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IACnB;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;IACZ;;;;OAIG;IACH,UAAU,CAAC,EAAE;QACX,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;KACpB,CAAC;IACF;;OAEG;IACH,KAAK,EAAE,CAAC;QAAE,IAAI,EAAE,SAAS,CAAC;QAAC,EAAE,EAAE,MAAM,CAAA;KAAE,GAAG,SAAS,CAAC,EAAE,CAAC;IACvD;;OAEG;IACH,UAAU,CAAC,EAAE,CAAC,aAAa,GAAG,eAAe,CAAC,EAAE,CAAC;IACjD;;;OAGG;IACH,0BAA0B,CAAC,EAAE;QAC3B,YAAY,CAAC,EAAE,MAAM,CAAC;QACtB,aAAa,CAAC,EAAE,MAAM,CAAC;KACxB,EAAE,CAAC;IACJ;;;;OAIG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;CAC1B;AAED;;;;GAIG;AACH,MAAM,WAAW,UAAW,SAAQ,iBAAiB;IACnD;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,gCAAgC;IAC/C,wEAAwE;IACxE,UAAU,EAAE,OAAO,CAAC;IACpB,IAAI,EAAE,mBAAmB,CAAC;IAC1B,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;CACpC;AAED,MAAM,WAAW,iBAAkB,SAAQ,YAAY;IAErD,IAAI,CAAC,KAAK,EAAE,uBAAuB,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;IAC9D,EAAE,CAAC,KAAK,EAAE,uBAAuB,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;IAC7E,IAAI,CAAC,KAAK,EAAE,uBAAuB,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;CAChF;AAED,qBAAa,iBAAkB,SAAQ,YAAa,YAAW,eAAe;IAC5E;;OAEG;IACH,SAAS,CAAC,aAAa,CAAC,EAAE,iBAAiB,CAAC;IAC5C;;OAEG;IACH,IAAI,EAAE,qBAAqB,CAAC;IAC5B;;OAEG;IACH,SAAS,CAAC,MAAM,EAAE,uBAAuB,CAA+B;IACxE;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;IAExC;;;OAGG;gBACS,IAAI,CAAC,EAAE,qBAAqB;IAWxC;;;OAGG;WACU,MAAM,CAAC,IAAI,CAAC,EAAE,qBAAqB,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAQ7E;;;;OAIG;IACG,KAAK,CAAC,aAAa,GAAE,OAAe,GAAG,OAAO,CAAC,IAAI,CAAC;IAmD1D;;;OAGG;IACH,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,uBAAuB,GAAG,IAAI;IAK9D;;;OAGG;IACH,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI;IAKvD;;;OAGG;cACa,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAW1D;;OAEG;cACa,eAAe,CAC7B,aAAa,GAAE,OAAe,GAC7B,OAAO,CAAC,gCAAgC,CAAC;IA0E5C;;;;OAIG;IACG,gBAAgB,CAAC,aAAa,GAAE,OAAe,GAAG,OAAO,CAAC,IAAI,CAAC;IA2DrE;;;;;OAKG;IACG,IAAI,CAAC,UAAU,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IACjD;;;OAGG;IACG,IAAI,CAAC,cAAc,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IA2CtD;;;;;;;;OAQG;IACG,OAAO,CAAC,KAAK,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAC5C;;;;;;OAMG;IACG,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IA8D/C;;OAEG;IACH,IAAI,YAAY,IAAI,iBAAiB,GAAG,SAAS,CAEhD;IAED;;OAEG;IACH,IAAI,KAAK,IAAI,uBAAuB,CAEnC;IAED;;;OAGG;IACG,cAAc,IAAI,OAAO,CAAC,iBAAiB,CAAC;IA2DlD;;;;;;;OAOG;IACH,MAAM,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM;IAoBlD;;;;;OAKG;IACG,UAAU,CAAC,IAAI,EAAE,mBAAmB,GAAG,OAAO,CAAC,IAAI,CAAC;IAiF1D;;;;OAIG;IACH,SAAS,CAAC,gBAAgB,IAAI,OAAO;CAStC;AAED,eAAe,iBAAiB,CAAC"}