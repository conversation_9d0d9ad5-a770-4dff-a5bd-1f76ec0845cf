# Excel Chat AI Backend

A comprehensive Node.js/Express.js API service that provides intelligent Excel assistance through OpenAI's platform. This backend serves as the bridge between Excel Add-in frontends and OpenAI's Assistant API, handling data processing, conversation management, and Excel-specific operations.

## 🚀 Features

- **AI-Powered Excel Assistance**: Integration with OpenAI's Assistant API for intelligent Excel help
- **File Processing**: Upload and analyze Excel files (.xlsx, .xls, .csv)
- **Data Optimization**: Smart data compression and optimization for AI consumption
- **Conversation Management**: Persistent chat sessions with context awareness
- **User Authentication**: JWT-based authentication with role-based access control
- **Rate Limiting**: Subscription-based rate limiting and security measures
- **Monitoring**: Comprehensive logging, metrics, and health checks
- **Scalable Architecture**: Docker-ready with production deployment configurations

## 📋 Prerequisites

- Node.js 18+
- MongoDB 5.0+
- OpenAI API Key
- Docker & Docker Compose (for containerized deployment)

## 🛠️ Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd excel-chat-backend
npm install
```

### 2. Environment Configuration

```bash
# Run the interactive setup script
npm run db:setup

# Or manually copy and configure
cp .env.example .env
# Edit .env with your configuration
```

### 3. Database Setup

```bash
npm run db:migrate
```

### 4. Start Development Server

```bash
npm run dev
```

The API will be available at `http://localhost:3001`

## 🐳 Docker Deployment

### Development

```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f api-dev
```

### Production

```bash
# Deploy to production
./scripts/deploy.sh production

# Or manually
docker-compose up -d
```

## 📚 API Documentation

### Authentication Endpoints

```http
POST   /api/auth/register     # User registration
POST   /api/auth/login        # User login
POST   /api/auth/refresh      # Refresh JWT token
POST   /api/auth/logout       # User logout
GET    /api/auth/profile      # Get user profile
PUT    /api/auth/profile      # Update user profile
```

### Chat Endpoints

```http
POST   /api/chat/message      # Send chat message
GET    /api/chat/history/:id  # Get conversation history
DELETE /api/chat/clear/:id    # Clear conversation
POST   /api/chat/feedback     # Submit feedback
GET    /api/chat/conversations # Get user conversations
```

### Excel Endpoints

```http
POST   /api/excel/analyze     # Analyze Excel data
POST   /api/excel/formula     # Generate formulas
POST   /api/excel/upload      # Upload Excel file
GET    /api/excel/sessions    # Get user sessions
DELETE /api/excel/session/:id # Delete session
```

### System Endpoints

```http
GET    /api/health           # Health check
GET    /api/metrics          # Prometheus metrics
GET    /api/version          # API version info
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run specific test types
npm run test:unit
npm run test:integration

# Generate coverage report
npm run test:coverage

# Watch mode for development
npm run test:watch
```

## 📊 Monitoring

### Health Check

```bash
curl http://localhost:3001/api/health
```

### Metrics (Prometheus)

```bash
curl http://localhost:3001/api/metrics
```

### Logs

Logs are stored in the `logs/` directory:
- `app.log` - General application logs
- `error.log` - Error logs only
- `api.log` - API request logs

## 🔧 Configuration

### Environment Variables

Key environment variables (see `.env.example` for complete list):

```env
# Server
NODE_ENV=development
PORT=3001

# Database
MONGODB_URI=mongodb://localhost:27017/excel-chat-ai

# OpenAI
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ASSISTANT_ID=asst_your_assistant_id_here

# Authentication
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=7d

# Security
CORS_ORIGINS=http://localhost:3000
RATE_LIMIT_MAX_REQUESTS=100
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Excel Add-in  │───▶│  Backend API    │───▶│   OpenAI API    │
│   (Frontend)    │    │   (Node.js)     │    │   (Assistant)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │    Database     │
                       │   (MongoDB)     │
                       └─────────────────┘
```

## 📁 Project Structure

```
excel-chat-backend/
├── src/
│   ├── controllers/        # Request handlers
│   ├── middleware/         # Express middleware
│   ├── models/            # Database models
│   ├── services/          # Business logic
│   ├── routes/            # API routes
│   ├── utils/             # Utility functions
│   └── config/            # Configuration files
├── tests/                 # Test files
├── scripts/               # Deployment scripts
├── logs/                  # Log files
├── uploads/               # File uploads
└── docs/                  # Documentation
```

## 🔐 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Multiple rate limiting strategies
- **Input Validation**: Comprehensive request validation
- **CORS Protection**: Configurable CORS policies
- **Helmet Security**: Security headers and protections
- **File Upload Security**: Secure file handling with type validation

## 🚀 Deployment

### Production Deployment

1. **Environment Setup**:
   ```bash
   cp .env.example .env.production
   # Configure production values
   ```

2. **Deploy with Script**:
   ```bash
   ./scripts/deploy.sh production
   ```

3. **Manual Deployment**:
   ```bash
   docker-compose -f docker-compose.yml up -d
   ```

### Staging Deployment

```bash
./scripts/deploy.sh staging
```

## 📈 Performance

- **Connection Pooling**: MongoDB connection pooling
- **Response Caching**: Intelligent caching strategies
- **Data Compression**: Automatic response compression
- **File Optimization**: Smart Excel file processing
- **Async Processing**: Non-blocking operations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` directory
- **Issues**: Report bugs via GitHub Issues
- **Health Check**: `GET /api/health`
- **Logs**: Check `logs/` directory for debugging

## 🔄 Development Workflow

```bash
# Start development
npm run dev

# Run tests
npm test

# Check code quality
npm run lint
npm run format

# Build for production
npm run build

# Deploy
./scripts/deploy.sh production
```

---

**Excel Chat AI Backend** - Empowering intelligent Excel assistance through AI 🚀
