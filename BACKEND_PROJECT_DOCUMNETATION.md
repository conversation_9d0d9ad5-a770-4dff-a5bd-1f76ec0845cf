# Excel Chat AI Backend - Project Documentation

## 📋 **Project Overview**

The Excel Chat AI Backend is a Node.js/Express.js API service that provides intelligent Excel assistance through OpenAI's platform. It serves as the bridge between the Excel Add-in frontend and OpenAI's Assistant API, handling data processing, conversation management, and Excel-specific operations.

## 🏗️ **Architecture Overview**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Excel Add-in  │───▶│  Backend API    │───▶│   OpenAI API    │
│   (Frontend)    │    │   (Node.js)     │    │   (Assistant)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │    Database     │
                       │   (MongoDB)     │
                       └─────────────────┘
```

## 📁 **Project Structure**

```
excel-chat-backend/
├── src/
│   ├── controllers/
│   │   ├── chatController.js
│   │   ├── excelController.js
│   │   └── authController.js
│   ├── middleware/
│   │   ├── auth.js
│   │   ├── rateLimiter.js
│   │   └── validation.js
│   ├── models/
│   │   ├── Conversation.js
│   │   ├── User.js
│   │   └── ExcelSession.js
│   ├── services/
│   │   ├── openaiService.js
│   │   ├── excelProcessor.js
│   │   └── dataOptimizer.js
│   ├── routes/
│   │   ├── chat.js
│   │   ├── excel.js
│   │   └── auth.js
│   ├── utils/
│   │   ├── logger.js
│   │   ├── errorHandler.js
│   │   └── helpers.js
│   └── config/
│       ├── database.js
│       ├── openai.js
│       └── environment.js
├── tests/
│   ├── unit/
│   ├── integration/
│   └── fixtures/
├── docs/
│   ├── api/
│   └── deployment/
├── scripts/
│   ├── setup.js
│   └── migrate.js
├── .env.example
├── package.json
├── Dockerfile
├── docker-compose.yml
└── README.md
```

## 🚀 **Getting Started**

### **Prerequisites**
- Node.js 18+ 
- MongoDB 5.0+
- OpenAI API Key
- Excel Add-in Development Kit

### **Installation**

1. **Clone and Setup**
```bash
git clone <repository-url>
cd excel-chat-backend
npm install
```

2. **Environment Configuration**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Database Setup**
```bash
npm run db:setup
npm run db:migrate
```

4. **Start Development Server**
```bash
npm run dev
```

## ⚙️ **Configuration**

### **Environment Variables**

```env
# Server Configuration
NODE_ENV=development
PORT=3001
HOST=localhost

# Database
MONGODB_URI=mongodb+srv://vutov23:<EMAIL>/
MONGODB_TEST_URI=mongodb+srv://vutov23:<EMAIL>/

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ASSISTANT_ID=asst_your_assistant_id_here
OPENAI_MODEL=gpt-4-turbo-preview

# Authentication
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Security
CORS_ORIGINS=https://localhost:3000,https://your-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads
ALLOWED_FILE_TYPES=.xlsx,.xls,.csv

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
```

## 🛠️ **API Endpoints**

### **Authentication**
```http
POST   /api/auth/register     # User registration
POST   /api/auth/login        # User login
POST   /api/auth/refresh      # Refresh JWT token
POST   /api/auth/logout       # User logout
GET    /api/auth/profile      # Get user profile
```

### **Chat Operations**
```http
POST   /api/chat/message      # Send chat message
GET    /api/chat/history/:id  # Get conversation history
DELETE /api/chat/clear/:id    # Clear conversation
POST   /api/chat/feedback     # Submit feedback
```

### **Excel Operations**
```http
POST   /api/excel/analyze     # Analyze Excel data
POST   /api/excel/formula     # Generate formulas
POST   /api/excel/upload      # Upload Excel file
GET    /api/excel/sessions    # Get user sessions
DELETE /api/excel/session/:id # Delete session
```

### **System**
```http
GET    /api/health           # Health check
GET    /api/metrics          # System metrics
GET    /api/version          # API version
```

## 📊 **Core Services**

### **OpenAI Service**
```javascript
class OpenAIService {
  async createAssistant(config) {
    // Create specialized Excel assistant
  }
  
  async sendMessage(threadId, message, excelData) {
    // Send message with Excel context
  }
  
  async handleFunctionCall(functionName, parameters) {
    // Handle Excel-specific function calls
  }
  
  async getConversationHistory(threadId) {
    // Retrieve conversation history
  }
}
```

### **Excel Processor Service**
```javascript
class ExcelProcessorService {
  async analyzeData(excelData) {
    // Analyze Excel data structure and content
  }
  
  async optimizeForAI(data, targetSize) {
    // Optimize data size for AI consumption
  }
  
  async generateFormula(requirements, context) {
    // Generate Excel formulas based on requirements
  }
  
  async processFile(fileBuffer, options) {
    // Process uploaded Excel files
  }
}
```

### **Data Optimizer Service**
```javascript
class DataOptimizerService {
  async compressData(data, compressionLevel) {
    // Compress data while preserving meaning
  }
  
  async sampleLargeDataset(data, maxRows, strategy) {
    // Sample large datasets intelligently
  }
  
  async summarizeData(data, summaryType) {
    // Create data summaries for AI consumption
  }
}
```

## 🔐 **Security Features**

### **Authentication & Authorization**
- JWT-based authentication
- Role-based access control
- Session management
- Password hashing with bcrypt

### **Rate Limiting**
```javascript
const rateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP',
  standardHeaders: true,
  legacyHeaders: false
});
```

### **Input Validation**
```javascript
const messageValidation = {
  body: Joi.object({
    message: Joi.string().min(1).max(2000).required(),
    excelData: Joi.object().optional(),
    threadId: Joi.string().optional()
  })
};
```

### **CORS Configuration**
```javascript
const corsOptions = {
  origin: process.env.CORS_ORIGINS.split(','),
  credentials: true,
  optionsSuccessStatus: 200
};
```

## 📈 **Monitoring & Logging**

### **Winston Logger Configuration**
```javascript
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

### **Metrics Collection**
```javascript
const promClient = require('prom-client');

const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code']
});

const openaiApiCalls = new promClient.Counter({
  name: 'openai_api_calls_total',
  help: 'Total number of OpenAI API calls',
  labelNames: ['model', 'status']
});
```

## 🧪 **Testing**

### **Test Structure**
```javascript
// Unit Tests
describe('ExcelProcessorService', () => {
  test('should analyze Excel data correctly', async () => {
    const result = await excelProcessor.analyzeData(mockData);
    expect(result).toHaveProperty('statistics');
    expect(result.statistics.rowCount).toBe(100);
  });
});

// Integration Tests
describe('Chat API', () => {
  test('POST /api/chat/message should return AI response', async () => {
    const response = await request(app)
      .post('/api/chat/message')
      .send({ message: 'Help with Excel formulas' })
      .expect(200);
    
    expect(response.body).toHaveProperty('response');
  });
});
```

### **Test Commands**
```bash
npm test              # Run all tests
npm run test:unit     # Run unit tests only
npm run test:integration # Run integration tests
npm run test:coverage # Generate coverage report
npm run test:watch    # Watch mode for development
```

## 🚀 **Deployment**

### **Docker Deployment**
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
EXPOSE 3001

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/api/health || exit 1

CMD ["node", "src/server.js"]
```

### **Docker Compose**
```yaml
version: '3.8'
services:
  api:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/excel-chat-ai
    depends_on:
      - mongo
    restart: unless-stopped

  mongo:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    restart: unless-stopped

volumes:
  mongo_data:
```

### **Production Deployment**
```bash
# Build and deploy
npm run build
npm run deploy:production

# Health check
curl https://api.your-domain.com/api/health

# Monitor logs
npm run logs:production
```

## 📊 **Performance Considerations**

### **Optimization Strategies**
- **Connection pooling** for MongoDB
- **Response caching** for frequent requests
- **Data compression** for large Excel files
- **Async processing** for heavy operations
- **Load balancing** for multiple instances

### **Monitoring Metrics**
- Response time percentiles
- Error rates by endpoint
- OpenAI API usage and costs
- Database query performance
- Memory and CPU utilization

## 🔧 **Development Workflow**

### **Code Standards**
- ESLint configuration for code quality
- Prettier for code formatting
- Husky for pre-commit hooks
- Conventional commits for changelog

### **Git Workflow**
```bash
# Feature development
git checkout -b feature/excel-formula-generator
git commit -m "feat: add formula generation endpoint"
git push origin feature/excel-formula-generator

# Create pull request and merge
```

### **Release Process**
```bash
npm version patch  # or minor/major
npm run changelog
git push --tags
npm run deploy:staging
npm run test:e2e
npm run deploy:production
```

This backend provides a robust, scalable foundation for the Excel Chat AI system with proper architecture, security, and monitoring capabilities.
