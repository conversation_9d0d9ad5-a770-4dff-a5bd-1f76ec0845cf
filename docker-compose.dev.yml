version: '3.8'

services:
  # Development API service
  api-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: excel-chat-api-dev
    restart: unless-stopped
    ports:
      - "${PORT:-3001}:3001"
      - "9229:9229" # Node.js debugger port
    environment:
      - NODE_ENV=development
      - PORT=3001
      - MONGODB_URI=mongodb://mongo-dev:27017/excel-chat-ai-dev
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_ASSISTANT_ID=${OPENAI_ASSISTANT_ID}
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret-key}
      - CORS_ORIGINS=http://localhost:3000,http://localhost:3001
      - LOG_LEVEL=debug
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./temp:/app/temp
    depends_on:
      mongo-dev:
        condition: service_healthy
    networks:
      - excel-chat-dev-network
    command: npm run dev

  # Development MongoDB
  mongo-dev:
    image: mongo:5.0
    container_name: excel-chat-mongo-dev
    restart: unless-stopped
    ports:
      - "${MONGO_DEV_PORT:-27018}:27017"
    environment:
      - MONGO_INITDB_DATABASE=excel-chat-ai-dev
    volumes:
      - mongo_dev_data:/data/db
    networks:
      - excel-chat-dev-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongo localhost:27017/excel-chat-ai-dev --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Development Redis
  redis-dev:
    image: redis:7-alpine
    container_name: excel-chat-redis-dev
    restart: unless-stopped
    ports:
      - "${REDIS_DEV_PORT:-6380}:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    networks:
      - excel-chat-dev-network

  # MongoDB Express for database management
  mongo-express:
    image: mongo-express:latest
    container_name: excel-chat-mongo-express
    restart: unless-stopped
    ports:
      - "${MONGO_EXPRESS_PORT:-8081}:8081"
    environment:
      - ME_CONFIG_MONGODB_SERVER=mongo-dev
      - ME_CONFIG_MONGODB_PORT=27017
      - ME_CONFIG_MONGODB_ENABLE_ADMIN=true
      - ME_CONFIG_BASICAUTH_USERNAME=${MONGO_EXPRESS_USER:-admin}
      - ME_CONFIG_BASICAUTH_PASSWORD=${MONGO_EXPRESS_PASSWORD:-password}
    depends_on:
      - mongo-dev
    networks:
      - excel-chat-dev-network

  # Test runner service
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile
      target: testing
    container_name: excel-chat-test-runner
    environment:
      - NODE_ENV=test
      - MONGODB_TEST_URI=mongodb://mongo-test:27017/excel-chat-ai-test
      - JWT_SECRET=test-jwt-secret-key
      - OPENAI_API_KEY=test-openai-key
    volumes:
      - .:/app
      - /app/node_modules
      - ./coverage:/app/coverage
    depends_on:
      mongo-test:
        condition: service_healthy
    networks:
      - excel-chat-dev-network
    command: npm test
    profiles:
      - testing

  # Test MongoDB
  mongo-test:
    image: mongo:5.0
    container_name: excel-chat-mongo-test
    environment:
      - MONGO_INITDB_DATABASE=excel-chat-ai-test
    volumes:
      - mongo_test_data:/data/db
    networks:
      - excel-chat-dev-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongo localhost:27017/excel-chat-ai-test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    profiles:
      - testing

  # Mailhog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: excel-chat-mailhog
    restart: unless-stopped
    ports:
      - "${MAILHOG_SMTP_PORT:-1025}:1025"
      - "${MAILHOG_WEB_PORT:-8025}:8025"
    networks:
      - excel-chat-dev-network

volumes:
  mongo_dev_data:
    driver: local
  mongo_test_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  excel-chat-dev-network:
    driver: bridge
