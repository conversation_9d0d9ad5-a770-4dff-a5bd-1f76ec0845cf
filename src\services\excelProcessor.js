const XLSX = require('xlsx');
const fs = require('fs').promises;
const path = require('path');

class ExcelProcessorService {
  constructor() {
    this.supportedFormats = ['.xlsx', '.xls', '.csv'];
  }

  async processFile(fileBuffer, options = {}) {
    try {
      const {
        fileName = 'unknown.xlsx',
        includeFormulas = true,
        maxRows = 10000,
        detectDataTypes = true
      } = options;

      console.log(`📊 Processing Excel file: ${fileName}`);
      const startTime = Date.now();

      // Read workbook from buffer
      const workbook = XLSX.read(fileBuffer, {
        type: 'buffer',
        cellFormula: includeFormulas,
        cellDates: true,
        cellNF: false,
        cellStyles: false
      });

      const result = {
        fileName,
        fileSize: fileBuffer.length,
        sheetCount: workbook.SheetNames.length,
        sheets: [],
        metadata: this.extractMetadata(workbook),
        processingTime: 0,
        warnings: []
      };

      // Process each sheet
      for (let i = 0; i < workbook.SheetNames.length; i++) {
        const sheetName = workbook.SheetNames[i];
        const worksheet = workbook.Sheets[sheetName];
        
        const sheetData = await this.processSheet(worksheet, {
          name: sheetName,
          index: i,
          maxRows,
          detectDataTypes
        });

        result.sheets.push(sheetData);

        if (sheetData.rowCount > maxRows) {
          result.warnings.push(`Sheet "${sheetName}" has ${sheetData.rowCount} rows, truncated to ${maxRows}`);
        }
      }

      result.processingTime = Date.now() - startTime;
      console.log(`✅ File processed in ${result.processingTime}ms`);

      return result;
    } catch (error) {
      console.error('❌ Excel processing error:', error.message);
      throw new Error(`Excel processing failed: ${error.message}`);
    }
  }

  async processSheet(worksheet, options = {}) {
    try {
      const { name, index, maxRows = 10000, detectDataTypes = true } = options;

      // Get sheet range
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
      const rowCount = range.e.r + 1;
      const columnCount = range.e.c + 1;

      // Convert to JSON with limited rows
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        range: maxRows ? `A1:${XLSX.utils.encode_col(range.e.c)}${Math.min(maxRows, rowCount)}` : undefined,
        raw: false,
        dateNF: 'yyyy-mm-dd'
      });

      // Detect if first row contains headers
      const hasHeaders = this.detectHeaders(jsonData);

      // Get column information
      const columns = this.analyzeColumns(jsonData, hasHeaders, detectDataTypes);

      // Calculate statistics
      const statistics = this.calculateSheetStatistics(worksheet, range);

      return {
        name,
        index,
        rowCount,
        columnCount,
        hasHeaders,
        data: jsonData,
        columns,
        statistics,
        range: worksheet['!ref']
      };
    } catch (error) {
      console.error(`❌ Sheet processing error (${options.name}):`, error.message);
      throw error;
    }
  }

  detectHeaders(data) {
    if (!data || data.length < 2) return false;

    const firstRow = data[0];
    const secondRow = data[1];

    if (!firstRow || !secondRow) return false;

    // Check if first row contains mostly strings and second row contains different types
    let stringCount = 0;
    let totalCells = 0;

    for (let i = 0; i < Math.min(firstRow.length, secondRow.length); i++) {
      if (firstRow[i] !== undefined && firstRow[i] !== null && firstRow[i] !== '') {
        totalCells++;
        if (typeof firstRow[i] === 'string' && isNaN(firstRow[i])) {
          stringCount++;
        }
      }
    }

    return totalCells > 0 && (stringCount / totalCells) > 0.7;
  }

  analyzeColumns(data, hasHeaders, detectDataTypes) {
    if (!data || data.length === 0) return [];

    const headerRow = hasHeaders ? data[0] : null;
    const dataStartIndex = hasHeaders ? 1 : 0;
    const columns = [];

    // Determine number of columns
    const maxColumns = Math.max(...data.map(row => row ? row.length : 0));

    for (let colIndex = 0; colIndex < maxColumns; colIndex++) {
      const columnName = headerRow && headerRow[colIndex] 
        ? String(headerRow[colIndex]) 
        : `Column ${colIndex + 1}`;

      const columnData = {
        index: colIndex,
        name: columnName,
        type: 'mixed',
        samples: [],
        statistics: {
          totalCells: 0,
          emptyCells: 0,
          uniqueValues: new Set()
        }
      };

      // Analyze column data
      if (detectDataTypes) {
        const values = [];
        for (let rowIndex = dataStartIndex; rowIndex < Math.min(data.length, dataStartIndex + 100); rowIndex++) {
          const cellValue = data[rowIndex] ? data[rowIndex][colIndex] : undefined;
          if (cellValue !== undefined && cellValue !== null && cellValue !== '') {
            values.push(cellValue);
            columnData.statistics.uniqueValues.add(cellValue);
          }
          columnData.statistics.totalCells++;
          if (!cellValue) columnData.statistics.emptyCells++;
        }

        columnData.type = this.detectColumnType(values);
        columnData.samples = values.slice(0, 5);
        columnData.statistics.uniqueValues = columnData.statistics.uniqueValues.size;
      }

      columns.push(columnData);
    }

    return columns;
  }

  detectColumnType(values) {
    if (values.length === 0) return 'empty';

    const types = {
      number: 0,
      date: 0,
      boolean: 0,
      string: 0
    };

    for (const value of values) {
      if (typeof value === 'number' || (!isNaN(value) && !isNaN(parseFloat(value)))) {
        types.number++;
      } else if (value instanceof Date || this.isDateString(value)) {
        types.date++;
      } else if (typeof value === 'boolean' || value === 'true' || value === 'false') {
        types.boolean++;
      } else {
        types.string++;
      }
    }

    // Return the most common type
    const maxType = Object.keys(types).reduce((a, b) => types[a] > types[b] ? a : b);
    
    // If no clear majority, return mixed
    if (types[maxType] / values.length < 0.7) {
      return 'mixed';
    }

    return maxType;
  }

  isDateString(value) {
    if (typeof value !== 'string') return false;
    const date = new Date(value);
    return !isNaN(date.getTime()) && value.match(/\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4}|\d{2}-\d{2}-\d{4}/);
  }

  calculateSheetStatistics(worksheet, range) {
    const statistics = {
      totalCells: (range.e.r + 1) * (range.e.c + 1),
      emptyCells: 0,
      formulaCells: 0,
      numericCells: 0,
      textCells: 0
    };

    // Count different cell types
    for (let row = range.s.r; row <= range.e.r; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];

        if (!cell) {
          statistics.emptyCells++;
        } else if (cell.f) {
          statistics.formulaCells++;
        } else if (cell.t === 'n') {
          statistics.numericCells++;
        } else if (cell.t === 's') {
          statistics.textCells++;
        }
      }
    }

    return statistics;
  }

  extractMetadata(workbook) {
    const props = workbook.Props || {};
    return {
      creator: props.Author || 'Unknown',
      lastModified: props.ModifiedDate || null,
      application: props.Application || 'Unknown',
      version: props.AppVersion || 'Unknown',
      created: props.CreatedDate || null
    };
  }

  async analyzeData(data, analysisType = 'structure') {
    try {
      console.log(`🔍 Analyzing data: ${analysisType}`);

      switch (analysisType) {
        case 'structure':
          return this.analyzeStructure(data);
        case 'statistics':
          return this.analyzeStatistics(data);
        case 'patterns':
          return this.analyzePatterns(data);
        case 'quality':
          return this.analyzeQuality(data);
        default:
          throw new Error(`Unknown analysis type: ${analysisType}`);
      }
    } catch (error) {
      console.error('❌ Data analysis error:', error.message);
      throw error;
    }
  }

  analyzeStructure(data) {
    return {
      type: 'structure',
      result: {
        sheetCount: data.sheets ? data.sheets.length : 0,
        totalRows: data.sheets ? data.sheets.reduce((sum, sheet) => sum + sheet.rowCount, 0) : 0,
        totalColumns: data.sheets ? Math.max(...data.sheets.map(sheet => sheet.columnCount)) : 0,
        hasHeaders: data.sheets ? data.sheets.some(sheet => sheet.hasHeaders) : false,
        dataTypes: data.sheets ? this.getUniqueDataTypes(data.sheets) : []
      }
    };
  }

  analyzeStatistics(data) {
    const stats = {
      type: 'statistics',
      result: {
        sheets: []
      }
    };

    if (data.sheets) {
      for (const sheet of data.sheets) {
        const sheetStats = {
          name: sheet.name,
          rowCount: sheet.rowCount,
          columnCount: sheet.columnCount,
          ...sheet.statistics
        };
        stats.result.sheets.push(sheetStats);
      }
    }

    return stats;
  }

  analyzePatterns(data) {
    // Simplified pattern analysis
    return {
      type: 'patterns',
      result: {
        message: 'Pattern analysis would identify trends, duplicates, and anomalies in the data'
      }
    };
  }

  analyzeQuality(data) {
    // Simplified quality analysis
    return {
      type: 'quality',
      result: {
        message: 'Quality analysis would check for missing values, inconsistencies, and data integrity issues'
      }
    };
  }

  getUniqueDataTypes(sheets) {
    const types = new Set();
    for (const sheet of sheets) {
      if (sheet.columns) {
        for (const column of sheet.columns) {
          types.add(column.type);
        }
      }
    }
    return Array.from(types);
  }

  async generateFormula(requirement, context = {}) {
    try {
      console.log(`🧮 Generating formula for: ${requirement}`);

      // This is a simplified formula generator
      // In a real implementation, this would use AI or a more sophisticated algorithm
      const formulas = this.getCommonFormulas();
      
      const matchedFormula = this.matchRequirementToFormula(requirement, formulas);
      
      return {
        requirement,
        formula: matchedFormula.formula,
        explanation: matchedFormula.explanation,
        example: matchedFormula.example,
        context
      };
    } catch (error) {
      console.error('❌ Formula generation error:', error.message);
      throw error;
    }
  }

  getCommonFormulas() {
    return [
      {
        keywords: ['sum', 'total', 'add'],
        formula: 'SUM(A1:A10)',
        explanation: 'Adds all numbers in the specified range',
        example: 'SUM(A1:A10) adds all values from A1 to A10'
      },
      {
        keywords: ['average', 'mean'],
        formula: 'AVERAGE(A1:A10)',
        explanation: 'Calculates the average of numbers in the specified range',
        example: 'AVERAGE(A1:A10) calculates the mean of values from A1 to A10'
      },
      {
        keywords: ['count', 'number of'],
        formula: 'COUNT(A1:A10)',
        explanation: 'Counts the number of cells containing numbers',
        example: 'COUNT(A1:A10) counts numeric values from A1 to A10'
      },
      {
        keywords: ['maximum', 'max', 'highest'],
        formula: 'MAX(A1:A10)',
        explanation: 'Returns the largest value in the specified range',
        example: 'MAX(A1:A10) finds the highest value from A1 to A10'
      },
      {
        keywords: ['minimum', 'min', 'lowest'],
        formula: 'MIN(A1:A10)',
        explanation: 'Returns the smallest value in the specified range',
        example: 'MIN(A1:A10) finds the lowest value from A1 to A10'
      }
    ];
  }

  matchRequirementToFormula(requirement, formulas) {
    const lowerReq = requirement.toLowerCase();
    
    for (const formula of formulas) {
      if (formula.keywords.some(keyword => lowerReq.includes(keyword))) {
        return formula;
      }
    }

    // Default formula if no match found
    return {
      formula: 'SUM(A1:A10)',
      explanation: 'Default SUM formula - please specify your exact requirement for a more accurate formula',
      example: 'SUM(A1:A10) adds all values in the range A1 to A10'
    };
  }
}

// Create singleton instance
const excelProcessor = new ExcelProcessorService();

module.exports = excelProcessor;
