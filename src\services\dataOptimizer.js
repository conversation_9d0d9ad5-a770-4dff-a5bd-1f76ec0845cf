class DataOptimizerService {
  constructor() {
    this.maxTokens = 4000; // Default max tokens for AI consumption
    this.compressionStrategies = ['sampling', 'summarization', 'filtering', 'aggregation'];
  }

  async optimizeForAI(data, targetSize = this.maxTokens) {
    try {
      console.log(`🔧 Optimizing data for AI consumption, target size: ${targetSize} tokens`);
      const startTime = Date.now();

      // Estimate current size
      const currentSize = this.estimateTokenCount(data);
      
      if (currentSize <= targetSize) {
        console.log(`✅ Data already within target size: ${currentSize} tokens`);
        return {
          originalData: data,
          optimizedData: data,
          originalSize: currentSize,
          optimizedSize: currentSize,
          compressionRatio: 1,
          strategy: 'none',
          processingTime: Date.now() - startTime
        };
      }

      console.log(`📊 Current size: ${currentSize} tokens, target: ${targetSize} tokens`);

      // Choose optimization strategy based on data type and size
      const strategy = this.selectOptimizationStrategy(data, currentSize, targetSize);
      
      let optimizedData;
      switch (strategy) {
        case 'sampling':
          optimizedData = await this.sampleLargeDataset(data, targetSize);
          break;
        case 'summarization':
          optimizedData = await this.summarizeData(data, 'statistical');
          break;
        case 'filtering':
          optimizedData = await this.filterRelevantData(data);
          break;
        case 'aggregation':
          optimizedData = await this.aggregateData(data);
          break;
        default:
          optimizedData = await this.compressData(data, 0.5);
      }

      const optimizedSize = this.estimateTokenCount(optimizedData);
      const compressionRatio = optimizedSize / currentSize;

      console.log(`✅ Data optimized using ${strategy}: ${optimizedSize} tokens (${(compressionRatio * 100).toFixed(1)}% of original)`);

      return {
        originalData: data,
        optimizedData,
        originalSize: currentSize,
        optimizedSize,
        compressionRatio,
        strategy,
        processingTime: Date.now() - startTime
      };
    } catch (error) {
      console.error('❌ Data optimization error:', error.message);
      throw new Error(`Data optimization failed: ${error.message}`);
    }
  }

  estimateTokenCount(data) {
    try {
      // Rough estimation: 1 token ≈ 4 characters for English text
      const jsonString = JSON.stringify(data);
      return Math.ceil(jsonString.length / 4);
    } catch (error) {
      console.error('❌ Token estimation error:', error.message);
      return 0;
    }
  }

  selectOptimizationStrategy(data, currentSize, targetSize) {
    const compressionNeeded = currentSize / targetSize;

    if (compressionNeeded < 2) {
      return 'filtering';
    } else if (compressionNeeded < 5) {
      return 'sampling';
    } else if (compressionNeeded < 10) {
      return 'summarization';
    } else {
      return 'aggregation';
    }
  }

  async compressData(data, compressionLevel = 0.5) {
    try {
      console.log(`🗜️ Compressing data with level: ${compressionLevel}`);

      if (!data || typeof data !== 'object') {
        return data;
      }

      const compressed = JSON.parse(JSON.stringify(data)); // Deep clone

      if (data.sheets && Array.isArray(data.sheets)) {
        compressed.sheets = data.sheets.map(sheet => {
          const compressedSheet = { ...sheet };
          
          // Compress sheet data
          if (sheet.data && Array.isArray(sheet.data)) {
            const targetRows = Math.floor(sheet.data.length * compressionLevel);
            compressedSheet.data = this.sampleArray(sheet.data, targetRows);
          }

          // Simplify column information
          if (sheet.columns) {
            compressedSheet.columns = sheet.columns.map(col => ({
              name: col.name,
              type: col.type,
              samples: col.samples ? col.samples.slice(0, 2) : []
            }));
          }

          return compressedSheet;
        });
      }

      return compressed;
    } catch (error) {
      console.error('❌ Data compression error:', error.message);
      throw error;
    }
  }

  async sampleLargeDataset(data, maxRows = 1000, strategy = 'systematic') {
    try {
      console.log(`📊 Sampling dataset with strategy: ${strategy}, max rows: ${maxRows}`);

      if (!data || !data.sheets) {
        return data;
      }

      const sampled = JSON.parse(JSON.stringify(data)); // Deep clone

      sampled.sheets = data.sheets.map(sheet => {
        const sampledSheet = { ...sheet };
        
        if (sheet.data && Array.isArray(sheet.data) && sheet.data.length > maxRows) {
          switch (strategy) {
            case 'random':
              sampledSheet.data = this.randomSample(sheet.data, maxRows);
              break;
            case 'systematic':
              sampledSheet.data = this.systematicSample(sheet.data, maxRows);
              break;
            case 'stratified':
              sampledSheet.data = this.stratifiedSample(sheet.data, maxRows);
              break;
            default:
              sampledSheet.data = sheet.data.slice(0, maxRows);
          }
          
          sampledSheet.rowCount = sampledSheet.data.length;
          sampledSheet.isSampled = true;
          sampledSheet.originalRowCount = sheet.rowCount;
          sampledSheet.samplingStrategy = strategy;
        }

        return sampledSheet;
      });

      return sampled;
    } catch (error) {
      console.error('❌ Data sampling error:', error.message);
      throw error;
    }
  }

  randomSample(array, size) {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, size);
  }

  systematicSample(array, size) {
    if (array.length <= size) return array;
    
    const step = Math.floor(array.length / size);
    const sampled = [];
    
    for (let i = 0; i < array.length && sampled.length < size; i += step) {
      sampled.push(array[i]);
    }
    
    return sampled;
  }

  stratifiedSample(array, size) {
    // Simplified stratified sampling - in practice, this would be more sophisticated
    return this.systematicSample(array, size);
  }

  sampleArray(array, targetSize) {
    if (!Array.isArray(array) || array.length <= targetSize) {
      return array;
    }

    const step = Math.floor(array.length / targetSize);
    const sampled = [];
    
    for (let i = 0; i < array.length && sampled.length < targetSize; i += step) {
      sampled.push(array[i]);
    }
    
    return sampled;
  }

  async summarizeData(data, summaryType = 'statistical') {
    try {
      console.log(`📋 Summarizing data with type: ${summaryType}`);

      if (!data || !data.sheets) {
        return data;
      }

      const summary = {
        fileName: data.fileName,
        fileSize: data.fileSize,
        sheetCount: data.sheetCount,
        processingTime: data.processingTime,
        summaryType,
        sheets: []
      };

      for (const sheet of data.sheets) {
        const sheetSummary = {
          name: sheet.name,
          rowCount: sheet.rowCount,
          columnCount: sheet.columnCount,
          hasHeaders: sheet.hasHeaders,
          statistics: sheet.statistics
        };

        switch (summaryType) {
          case 'statistical':
            sheetSummary.columnSummary = this.createColumnSummary(sheet.columns);
            sheetSummary.dataSample = sheet.data ? sheet.data.slice(0, 5) : [];
            break;
          case 'structural':
            sheetSummary.dataTypes = sheet.columns ? sheet.columns.map(col => col.type) : [];
            sheetSummary.columnNames = sheet.columns ? sheet.columns.map(col => col.name) : [];
            break;
          case 'content':
            sheetSummary.preview = this.createContentPreview(sheet.data);
            break;
        }

        summary.sheets.push(sheetSummary);
      }

      return summary;
    } catch (error) {
      console.error('❌ Data summarization error:', error.message);
      throw error;
    }
  }

  createColumnSummary(columns) {
    if (!columns) return [];

    return columns.map(col => ({
      name: col.name,
      type: col.type,
      uniqueValues: col.statistics ? col.statistics.uniqueValues : 0,
      emptyCells: col.statistics ? col.statistics.emptyCells : 0,
      samples: col.samples ? col.samples.slice(0, 3) : []
    }));
  }

  createContentPreview(data) {
    if (!data || !Array.isArray(data)) return [];

    return {
      firstRows: data.slice(0, 3),
      lastRows: data.length > 3 ? data.slice(-2) : [],
      totalRows: data.length
    };
  }

  async filterRelevantData(data, criteria = {}) {
    try {
      console.log('🔍 Filtering relevant data');

      if (!data || !data.sheets) {
        return data;
      }

      const filtered = JSON.parse(JSON.stringify(data)); // Deep clone

      filtered.sheets = data.sheets.map(sheet => {
        const filteredSheet = { ...sheet };

        // Remove empty columns
        if (sheet.columns) {
          filteredSheet.columns = sheet.columns.filter(col => 
            col.statistics && col.statistics.emptyCells < col.statistics.totalCells * 0.9
          );
        }

        // Filter data based on non-empty columns
        if (sheet.data && filteredSheet.columns) {
          const relevantColumnIndices = filteredSheet.columns.map(col => col.index);
          
          filteredSheet.data = sheet.data.map(row => {
            if (!Array.isArray(row)) return row;
            return relevantColumnIndices.map(index => row[index]);
          }).filter(row => row.some(cell => cell !== undefined && cell !== null && cell !== ''));
        }

        return filteredSheet;
      });

      return filtered;
    } catch (error) {
      console.error('❌ Data filtering error:', error.message);
      throw error;
    }
  }

  async aggregateData(data) {
    try {
      console.log('📊 Aggregating data');

      if (!data || !data.sheets) {
        return data;
      }

      const aggregated = {
        fileName: data.fileName,
        fileSize: data.fileSize,
        sheetCount: data.sheetCount,
        totalRows: 0,
        totalColumns: 0,
        aggregatedSheets: []
      };

      for (const sheet of data.sheets) {
        aggregated.totalRows += sheet.rowCount;
        aggregated.totalColumns = Math.max(aggregated.totalColumns, sheet.columnCount);

        const sheetAggregation = {
          name: sheet.name,
          rowCount: sheet.rowCount,
          columnCount: sheet.columnCount,
          dataTypes: sheet.columns ? sheet.columns.map(col => col.type) : [],
          statistics: sheet.statistics,
          keyMetrics: this.calculateKeyMetrics(sheet)
        };

        aggregated.aggregatedSheets.push(sheetAggregation);
      }

      return aggregated;
    } catch (error) {
      console.error('❌ Data aggregation error:', error.message);
      throw error;
    }
  }

  calculateKeyMetrics(sheet) {
    const metrics = {
      dataCompleteness: 0,
      numericColumns: 0,
      textColumns: 0,
      dateColumns: 0
    };

    if (sheet.statistics) {
      const totalCells = sheet.statistics.totalCells;
      const emptyCells = sheet.statistics.emptyCells;
      metrics.dataCompleteness = totalCells > 0 ? ((totalCells - emptyCells) / totalCells) * 100 : 0;
    }

    if (sheet.columns) {
      metrics.numericColumns = sheet.columns.filter(col => col.type === 'number').length;
      metrics.textColumns = sheet.columns.filter(col => col.type === 'string').length;
      metrics.dateColumns = sheet.columns.filter(col => col.type === 'date').length;
    }

    return metrics;
  }
}

// Create singleton instance
const dataOptimizer = new DataOptimizerService();

module.exports = dataOptimizer;
